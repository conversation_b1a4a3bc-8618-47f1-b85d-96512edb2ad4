%% TW-dRPS Complete System - 基于顶部加权的RPS距离计算系统
% 完全符合方案要求的"总不一致度"模型实现
% 
% 主要特性:
% 1. 实现真正的"总不一致度"模型: OD_weighted = 1 - Total_Disagreement
% 2. 二元不一致度: Disagreement_d ∈ {0,1}
% 3. 位置权重分配: w_d 分配给位置d的重要性
% 4. 长度惩罚机制: L_long 处理不等长排列
% 5. 基于OWA算子和最大熵原理的权重生成
% 6. 完整的RPS距离计算和融合框架

clear; clc;
fprintf('=== TW-dRPS Complete System ===\n');
fprintf('基于顶部加权的随机排列集距离计算和融合系统\n');
fprintf('实现了完全符合方案要求的"总不一致度"模型\n\n');

%% 选择运行模式
fprintf('请选择运行模式:\n');
fprintf('1. 核心改进演示（推荐 - 快速了解改进效果）\n');
fprintf('2. 完整系统演示（详细的融合过程）\n');
fprintf('3. 算法验证测试（技术验证）\n');
fprintf('4. 全部运行\n');

choice = input('请输入选择 (1-4): ');

if isempty(choice) || choice < 1 || choice > 4
    choice = 1;
    fprintf('使用默认选择: 核心改进演示\n');
end

%% 运行选择的模式
switch choice
    case 1
        fprintf('\n正在运行核心改进演示...\n');
        fprintf('=====================================\n');
        run_core_improvement_demo();
        
    case 2
        fprintf('\n正在运行完整系统演示...\n');
        fprintf('=====================================\n');
        run_complete_system_demo();
        
    case 3
        fprintf('\n正在运行算法验证测试...\n');
        fprintf('=====================================\n');
        run_algorithm_validation();
        
    case 4
        fprintf('\n正在运行核心改进演示...\n');
        fprintf('=====================================\n');
        run_core_improvement_demo();
        
        fprintf('\n\n正在运行完整系统演示...\n');
        fprintf('=====================================\n');
        run_complete_system_demo();
        
        fprintf('\n\n正在运行算法验证测试...\n');
        fprintf('=====================================\n');
        run_algorithm_validation();
end

fprintf('\n=== TW-dRPS系统运行完成 ===\n');

%% ========================================================================
%% 主要演示函数
%% ========================================================================

function run_core_improvement_demo()
    %% 核心改进演示 - 展示"总不一致度"模型的顶部偏置效果
    
    fprintf('=== TW-dRPS核心改进演示 ===\n');
    fprintf('展示"总不一致度"模型的顶部偏置效果\n\n');
    
    %% 核心问题演示
    fprintf('问题描述:\n');
    fprintf('原始dRPS方法的核心缺陷：第1位的交换与最后1位的交换被赋予同等惩罚\n');
    fprintf('这在"冠军与亚军之争"远比"第99名与第100名之争"更重要的现实场景中是不合理的\n\n');
    
    %% 测试用例设计
    fprintf('测试用例设计:\n');
    fprintf('===============\n');
    
    % 基准排列
    base = {'A', 'B', 'C', 'D'};
    
    % 情况1：顶部交换（第1、2位）
    top_swap = {'B', 'A', 'C', 'D'};
    
    % 情况2：底部交换（第3、4位）
    bottom_swap = {'A', 'B', 'D', 'C'};
    
    % 情况3：中间交换（第2、3位）
    middle_swap = {'A', 'C', 'B', 'D'};
    
    fprintf('基准排列:   [%s]\n', strjoin(base, ', '));
    fprintf('顶部交换:   [%s] (第1、2位交换)\n', strjoin(top_swap, ', '));
    fprintf('中间交换:   [%s] (第2、3位交换)\n', strjoin(middle_swap, ', '));
    fprintf('底部交换:   [%s] (第3、4位交换)\n', strjoin(bottom_swap, ', '));
    
    %% 原始方法计算
    fprintf('\n原始方法计算结果:\n');
    fprintf('==================\n');
    
    od_orig_top = calculate_original_od(base, top_swap);
    od_orig_middle = calculate_original_od(base, middle_swap);
    od_orig_bottom = calculate_original_od(base, bottom_swap);
    
    fprintf('基准 vs 顶部交换:   OD = %.6f\n', od_orig_top);
    fprintf('基准 vs 中间交换:   OD = %.6f\n', od_orig_middle);
    fprintf('基准 vs 底部交换:   OD = %.6f\n', od_orig_bottom);
    
    fprintf('\n原始方法分析:\n');
    if abs(od_orig_top - od_orig_bottom) < 0.001
        fprintf('✗ 原始方法对顶部和底部交换给予相同惩罚 (差异: %.6f)\n', abs(od_orig_top - od_orig_bottom));
        fprintf('  这违背了"顶部更重要"的直觉\n');
    end
    
    %% 改进方法计算 - 不同Orness值
    fprintf('\n改进方法计算结果:\n');
    fprintf('==================\n');
    
    orness_values = [0.5, 0.7, 0.9];
    
    fprintf('Orness值  顶部交换    中间交换    底部交换    顶部vs底部差异\n');
    fprintf('------------------------------------------------------------\n');
    
    for orness = orness_values
        od_impr_top = calculate_improved_od(base, top_swap, orness);
        od_impr_middle = calculate_improved_od(base, middle_swap, orness);
        od_impr_bottom = calculate_improved_od(base, bottom_swap, orness);
        
        diff_top_bottom = od_impr_top - od_impr_bottom;
        
        fprintf('%.1f       %.6f    %.6f    %.6f    %+.6f\n', ...
            orness, od_impr_top, od_impr_middle, od_impr_bottom, diff_top_bottom);
    end
    
    %% 详细分析一个高Orness情况
    fprintf('\n详细分析 (Orness = 0.9):\n');
    fprintf('========================\n');
    
    orness_high = 0.9;
    
    fprintf('权重分布 (长度=4, Orness=%.1f):\n', orness_high);
    weights = generate_owa_weights(4, orness_high);
    fprintf('位置权重: [%.4f, %.4f, %.4f, %.4f]\n', weights(1), weights(2), weights(3), weights(4));
    fprintf('解释: 第1位权重最大(%.4f)，第4位权重最小(%.4f)\n', weights(1), weights(4));
    
    fprintf('\n顶部交换详细计算:\n');
    od_top_detailed = calculate_improved_od_detailed(base, top_swap, orness_high);
    
    fprintf('\n底部交换详细计算:\n');
    od_bottom_detailed = calculate_improved_od_detailed(base, bottom_swap, orness_high);
    
    %% 改进效果总结
    fprintf('\n改进效果总结:\n');
    fprintf('==============\n');
    
    od_orig_diff = abs(od_orig_top - od_orig_bottom);
    od_impr_diff = abs(calculate_improved_od(base, top_swap, 0.9) - calculate_improved_od(base, bottom_swap, 0.9));
    
    fprintf('原始方法: 顶部vs底部交换差异 = %.6f\n', od_orig_diff);
    fprintf('改进方法: 顶部vs底部交换差异 = %.6f\n', od_impr_diff);
    fprintf('改进倍数: %.2fx\n', od_impr_diff / max(od_orig_diff, 1e-10));
    
    if od_impr_diff > od_orig_diff * 2
        fprintf('✓ 改进方法显著增强了对位置重要性的感知能力\n');
    elseif od_impr_diff > od_orig_diff
        fprintf('✓ 改进方法适度提升了对位置重要性的感知能力\n');
    else
        fprintf('? 改进效果不明显，可能需要调整参数\n');
    end
    
    %% 实际应用场景示例
    fprintf('\n实际应用场景示例:\n');
    fprintf('==================\n');
    
    fprintf('1. 搜索引擎结果排序:\n');
    fprintf('   - 第1名与第2名的交换比第9名与第10名的交换更重要\n');
    fprintf('   - 用户主要关注前几个搜索结果\n\n');
    
    fprintf('2. 威胁等级评估:\n');
    fprintf('   - 高威胁与中威胁的混淆比中威胁与低威胁的混淆更严重\n');
    fprintf('   - 需要重点关注高风险项目的准确性\n\n');
    
    fprintf('3. 专家意见融合:\n');
    fprintf('   - 专家对重要问题的分歧需要更多关注\n');
    fprintf('   - 顶部排序的一致性更为关键\n\n');
    
    %% 参数选择指导
    fprintf('参数选择指导:\n');
    fprintf('==============\n');
    fprintf('Orness = 0.5: 均匀权重，等同于原始方法\n');
    fprintf('Orness = 0.7: 适度顶部偏置，适合一般应用\n');
    fprintf('Orness = 0.9: 强顶部偏置，适合高度重视顶部的场景\n');
    fprintf('Orness = 0.95: 极强顶部偏置，几乎只关注前几位\n\n');
    
    fprintf('建议:\n');
    fprintf('- 搜索引擎: Orness = 0.8-0.9\n');
    fprintf('- 风险评估: Orness = 0.85-0.95\n');
    fprintf('- 推荐系统: Orness = 0.7-0.8\n');
    fprintf('- 一般排序: Orness = 0.6-0.7\n');
    
    fprintf('\n=== 核心改进演示完成 ===\n');
    fprintf('TW-dRPS成功解决了原始方法"位置等权"的核心缺陷\n');
end

function run_complete_system_demo()
    %% 完整系统演示 - 展示整个RPS融合过程
    
    fprintf('=== TW-dRPS完整系统演示 ===\n');
    fprintf('基于"总不一致度"模型的RPS距离计算和融合系统\n\n');
    
    %% 1. 系统参数设置
    fprintf('步骤1: 系统参数设置\n');
    fprintf('==================\n');
    
    % 设置系统参数
    orness_original = 0.5;  % 原始方法等效的orness值
    orness_improved = 0.8;  % 改进方法的orness值
    
    fprintf('原始方法等效Orness: %.1f (均匀权重)\n', orness_original);
    fprintf('改进方法Orness: %.2f (顶部偏置)\n', orness_improved);
    
    %% 2. 创建测试RPS数据
    fprintf('\n步骤2: 创建测试RPS数据\n');
    fprintf('======================\n');
    
    % 定义元素集合
    elements = {'A', 'B', 'C', 'D'};
    fprintf('元素集合: %s\n', strjoin(elements, ', '));
    
    % 创建测试RPS - 设计为能最大化展示顶部偏置效果
    % RPS1: A在顶部优先
    rps1_pmf = containers.Map();
    rps1_pmf('A,B,C') = 0.6;
    rps1_pmf('A,C,B') = 0.3;
    rps1_pmf('B,A,C') = 0.1;
    
    % RPS2: B在顶部优先  
    rps2_pmf = containers.Map();
    rps2_pmf('B,A,C') = 0.7;
    rps2_pmf('B,C,A') = 0.2;
    rps2_pmf('A,B,C') = 0.1;
    
    % RPS3: C在顶部优先
    rps3_pmf = containers.Map();
    rps3_pmf('C,A,B') = 0.5;
    rps3_pmf('C,B,A') = 0.3;
    rps3_pmf('A,C,B') = 0.2;
    
    % 创建RPS结构
    rps1 = create_rps(elements, rps1_pmf);
    rps2 = create_rps(elements, rps2_pmf);
    rps3 = create_rps(elements, rps3_pmf);
    
    % 显示RPS信息
    display_rps_detailed(rps1, 'RPS1 (A优先)');
    display_rps_detailed(rps2, 'RPS2 (B优先)');
    display_rps_detailed(rps3, 'RPS3 (C优先)');
    
    %% 3. 权重生成验证
    fprintf('\n步骤3: OWA权重生成验证\n');
    fprintf('========================\n');
    
    % 测试不同长度的权重生成
    test_lengths = [2, 3, 4, 5];
    fprintf('权重生成测试 (Orness = %.2f):\n', orness_improved);
    fprintf('长度  权重分布\n');
    fprintf('------------------\n');
    
    for len = test_lengths
        weights = generate_owa_weights(len, orness_improved);
        fprintf('%d     [', len);
        for i = 1:len
            fprintf('%.4f', weights(i));
            if i < len, fprintf(', '); end
        end
        fprintf(']\n');
    end
    
    %% 4. 计算距离和相似度矩阵
    fprintf('\n步骤4: 计算距离和相似度矩阵\n');
    fprintf('============================\n');
    
    rps_list = {rps1, rps2, rps3};
    n_rps = length(rps_list);
    
    % 计算距离矩阵
    distance_matrix_orig = zeros(n_rps, n_rps);
    distance_matrix_impr = zeros(n_rps, n_rps);
    
    for i = 1:n_rps
        for j = 1:n_rps
            if i ~= j
                distance_matrix_orig(i,j) = calculate_rps_distance(rps_list{i}, rps_list{j}, 'original', orness_original);
                distance_matrix_impr(i,j) = calculate_rps_distance(rps_list{i}, rps_list{j}, 'improved', orness_improved);
            end
        end
    end
    
    % 计算相似度矩阵
    sim_matrix_orig = 1 - distance_matrix_orig;
    sim_matrix_impr = 1 - distance_matrix_impr;
    
    for i = 1:n_rps
        sim_matrix_orig(i,i) = 1;
        sim_matrix_impr(i,i) = 1;
    end
    
    fprintf('距离矩阵对比:\n');
    fprintf('原始方法距离矩阵:\n');
    disp(distance_matrix_orig);
    fprintf('改进方法距离矩阵:\n');
    disp(distance_matrix_impr);
    
    %% 5. 计算支持度和可信度
    fprintf('\n步骤5: 计算支持度和可信度\n');
    fprintf('==========================\n');
    
    % 计算支持度
    support_orig = zeros(n_rps, 1);
    support_impr = zeros(n_rps, 1);
    
    for i = 1:n_rps
        support_orig(i) = sum(sim_matrix_orig(i, :)) - 1;
        support_impr(i) = sum(sim_matrix_impr(i, :)) - 1;
    end
    
    % 计算可信度
    total_support_orig = sum(support_orig);
    total_support_impr = sum(support_impr);
    credibility_orig = support_orig / total_support_orig;
    credibility_impr = support_impr / total_support_impr;
    
    fprintf('支持度和可信度对比:\n');
    fprintf('RPS    支持度(原始)  支持度(改进)  可信度(原始)  可信度(改进)\n');
    fprintf('----------------------------------------------------------------\n');
    for i = 1:n_rps
        fprintf('RPS%d   %.6f      %.6f      %.6f      %.6f\n', ...
            i, support_orig(i), support_impr(i), credibility_orig(i), credibility_impr(i));
    end
    
    %% 6. 计算加权RPS子集和熵值
    fprintf('\n步骤6: 计算加权RPS子集和熵值\n');
    fprintf('============================\n');
    
    % 计算所有可能的两两组合
    combinations_2 = nchoosek(1:n_rps, 2);
    entropies_orig = [];
    entropies_impr = [];
    
    fprintf('所有两两组合的熵值:\n');
    fprintf('组合      原始方法熵值  改进方法熵值  熵值差异\n');
    fprintf('----------------------------------------------\n');
    
    for c = 1:size(combinations_2, 1)
        idx1 = combinations_2(c, 1);
        idx2 = combinations_2(c, 2);
        
        % 原始方法权重
        weight_orig = [credibility_orig(idx1), credibility_orig(idx2)];
        weight_orig = weight_orig / sum(weight_orig);
        
        % 改进方法权重
        weight_impr = [credibility_impr(idx1), credibility_impr(idx2)];
        weight_impr = weight_impr / sum(weight_impr);
        
        % 计算加权PMF
        weighted_pmf_orig = weight_orig(1) * rps_list{idx1}.pmf_vector + weight_orig(2) * rps_list{idx2}.pmf_vector;
        weighted_pmf_impr = weight_impr(1) * rps_list{idx1}.pmf_vector + weight_impr(2) * rps_list{idx2}.pmf_vector;
        
        % 计算熵
        entropy_orig = calculate_rps_entropy(weighted_pmf_orig);
        entropy_impr = calculate_rps_entropy(weighted_pmf_impr);
        
        entropies_orig(c) = entropy_orig;
        entropies_impr(c) = entropy_impr;
        
        fprintf('RPS%d+RPS%d  %.6f        %.6f        %+.6f\n', ...
            idx1, idx2, entropy_orig, entropy_impr, entropy_impr - entropy_orig);
    end
    
    % 找到最小熵组合
    [min_entropy_orig, min_idx_orig] = min(entropies_orig);
    [min_entropy_impr, min_idx_impr] = min(entropies_impr);
    
    fprintf('\n最优融合组合:\n');
    fprintf('原始方法: RPS%d+RPS%d (熵值: %.6f)\n', ...
        combinations_2(min_idx_orig, 1), combinations_2(min_idx_orig, 2), min_entropy_orig);
    fprintf('改进方法: RPS%d+RPS%d (熵值: %.6f)\n', ...
        combinations_2(min_idx_impr, 1), combinations_2(min_idx_impr, 2), min_entropy_impr);
    
    %% 7. 融合结果对比表格
    fprintf('\n步骤7: 融合结果对比表格\n');
    fprintf('========================\n');
    
    % 重新计算最优组合的加权PMF
    idx1_orig = combinations_2(min_idx_orig, 1);
    idx2_orig = combinations_2(min_idx_orig, 2);
    weight_orig_optimal = [credibility_orig(idx1_orig), credibility_orig(idx2_orig)];
    weight_orig_optimal = weight_orig_optimal / sum(weight_orig_optimal);
    optimal_pmf_orig = weight_orig_optimal(1) * rps_list{idx1_orig}.pmf_vector + weight_orig_optimal(2) * rps_list{idx2_orig}.pmf_vector;
    
    idx1_impr = combinations_2(min_idx_impr, 1);
    idx2_impr = combinations_2(min_idx_impr, 2);
    weight_impr_optimal = [credibility_impr(idx1_impr), credibility_impr(idx2_impr)];
    weight_impr_optimal = weight_impr_optimal / sum(weight_impr_optimal);
    optimal_pmf_impr = weight_impr_optimal(1) * rps_list{idx1_impr}.pmf_vector + weight_impr_optimal(2) * rps_list{idx2_impr}.pmf_vector;
    
    % 创建融合结果对比表格
    create_fusion_comparison_table(rps1, optimal_pmf_orig, optimal_pmf_impr, ...
        min_entropy_orig, min_entropy_impr, idx1_orig, idx2_orig, idx1_impr, idx2_impr);
    
    %% 8. 结果分析和验证
    fprintf('\n步骤8: 结果分析和验证\n');
    fprintf('====================\n');
    
    analyze_results(optimal_pmf_orig, optimal_pmf_impr, min_entropy_orig, min_entropy_impr, ...
        min_idx_orig, min_idx_impr, combinations_2);
    
    fprintf('\n=== 完整系统演示完成 ===\n');
end

function run_algorithm_validation()
    %% 算法验证测试 - 验证改进算法的正确性
    
    fprintf('=== TW-dRPS算法验证测试 ===\n\n');
    
    %% 测试1: 验证"总不一致度"模型
    fprintf('测试1: 验证"总不一致度"模型\n');
    fprintf('============================\n');
    
    % 测试用例：第1位交换 vs 第3位交换
    A1 = {'A', 'B', 'C'};
    B1 = {'B', 'A', 'C'};  % 第1、2位交换
    
    A2 = {'A', 'B', 'C'};
    B2 = {'A', 'C', 'B'};  % 第2、3位交换
    
    orness_high = 0.9;  % 强顶部偏置
    orness_low = 0.1;   % 弱顶部偏置
    
    fprintf('测试排列:\n');
    fprintf('A1 = [%s], B1 = [%s] (第1、2位交换)\n', strjoin(A1, ', '), strjoin(B1, ', '));
    fprintf('A2 = [%s], B2 = [%s] (第2、3位交换)\n', strjoin(A2, ', '), strjoin(B2, ', '));
    
    % 计算原始方法的OD
    od_orig_1 = calculate_original_od(A1, B1);
    od_orig_2 = calculate_original_od(A2, B2);
    
    % 计算改进方法的OD（不同orness值）
    od_impr_1_high = calculate_improved_od(A1, B1, orness_high);
    od_impr_2_high = calculate_improved_od(A2, B2, orness_high);
    
    od_impr_1_low = calculate_improved_od(A1, B1, orness_low);
    od_impr_2_low = calculate_improved_od(A2, B2, orness_low);
    
    fprintf('\n结果对比:\n');
    fprintf('方法                第1、2位交换  第2、3位交换  差异\n');
    fprintf('------------------------------------------------\n');
    fprintf('原始方法            %.6f      %.6f      %.6f\n', od_orig_1, od_orig_2, od_orig_1 - od_orig_2);
    fprintf('改进方法(α=%.1f)      %.6f      %.6f      %.6f\n', orness_high, od_impr_1_high, od_impr_2_high, od_impr_1_high - od_impr_2_high);
    fprintf('改进方法(α=%.1f)      %.6f      %.6f      %.6f\n', orness_low, od_impr_1_low, od_impr_2_low, od_impr_1_low - od_impr_2_low);
    
    fprintf('\n分析:\n');
    if abs(od_orig_1 - od_orig_2) < 0.001
        fprintf('✓ 原始方法对两种交换给予相同惩罚（符合预期的缺陷）\n');
    end
    
    if (od_impr_1_high - od_impr_2_high) > 0.01
        fprintf('✓ 高orness值时，改进方法对顶部交换给予更大惩罚\n');
    else
        fprintf('✗ 高orness值时，改进方法未能体现顶部偏置\n');
    end
    
    if abs(od_impr_1_low - od_impr_2_low) < abs(od_impr_1_high - od_impr_2_high)
        fprintf('✓ 低orness值时，改进方法的顶部偏置效果减弱\n');
    end
    
    %% 测试2: 验证长度惩罚机制
    fprintf('\n\n测试2: 验证长度惩罚机制\n');
    fprintf('========================\n');
    
    A3 = {'A', 'B'};
    B3 = {'A', 'B', 'C'};  % B3比A3长
    
    A4 = {'A', 'B', 'C'};
    B4 = {'A', 'B'};       % A4比B4长
    
    fprintf('测试排列:\n');
    fprintf('A3 = [%s], B3 = [%s] (长度: %d vs %d)\n', strjoin(A3, ', '), strjoin(B3, ', '), length(A3), length(B3));
    fprintf('A4 = [%s], B4 = [%s] (长度: %d vs %d)\n', strjoin(A4, ', '), strjoin(B4, ', '), length(A4), length(B4));
    
    od_impr_3 = calculate_improved_od(A3, B3, orness_high);
    od_impr_4 = calculate_improved_od(A4, B4, orness_high);
    
    fprintf('\n结果:\n');
    fprintf('不等长排列的OD值:\n');
    fprintf('A3 vs B3: %.6f\n', od_impr_3);
    fprintf('A4 vs B4: %.6f\n', od_impr_4);
    
    if od_impr_3 < 1 && od_impr_4 < 1
        fprintf('✓ 长度惩罚机制正常工作，不等长排列的OD值小于1\n');
    else
        fprintf('✗ 长度惩罚机制可能存在问题\n');
    end
    
    %% 测试3: 验证权重生成的正确性
    fprintf('\n\n测试3: 验证OWA权重生成\n');
    fprintf('======================\n');
    
    test_lengths = [3, 4, 5];
    test_orness = [0.1, 0.5, 0.9];
    
    fprintf('权重生成验证:\n');
    fprintf('长度  Orness  权重分布                    计算Orness  误差\n');
    fprintf('--------------------------------------------------------\n');
    
    for len = test_lengths
        for orn = test_orness
            weights = generate_owa_weights(len, orn);
            calculated_orness = calculate_orness_from_weights(weights);
            error = abs(calculated_orness - orn);
            
            fprintf('%d     %.1f     [', len, orn);
            for i = 1:len
                fprintf('%.3f', weights(i));
                if i < len, fprintf(' '); end
            end
            fprintf(']     %.6f     %.2e\n', calculated_orness, error);
            
            if error > 1e-4
                fprintf('  ✗ 权重生成误差过大\n');
            end
        end
    end
    
    %% 测试4: 验证边界情况
    fprintf('\n\n测试4: 边界情况测试\n');
    fprintf('==================\n');
    
    % 测试空排列
    empty1 = {};
    empty2 = {};
    single1 = {'A'};
    single2 = {'B'};
    
    od_empty = calculate_improved_od(empty1, empty2, 0.8);
    od_single_same = calculate_improved_od(single1, single1, 0.8);
    od_single_diff = calculate_improved_od(single1, single2, 0.8);
    
    fprintf('边界情况测试结果:\n');
    fprintf('空排列 vs 空排列: OD = %.6f (期望: 1.0)\n', od_empty);
    fprintf('单元素相同: OD = %.6f (期望: 1.0)\n', od_single_same);
    fprintf('单元素不同: OD = %.6f (期望: 0.0)\n', od_single_diff);
    
    % 验证边界情况
    boundary_tests_passed = 0;
    total_boundary_tests = 3;
    
    if abs(od_empty - 1.0) < 1e-6
        fprintf('✓ 空排列测试通过\n');
        boundary_tests_passed = boundary_tests_passed + 1;
    else
        fprintf('✗ 空排列测试失败\n');
    end
    
    if abs(od_single_same - 1.0) < 1e-6
        fprintf('✓ 单元素相同测试通过\n');
        boundary_tests_passed = boundary_tests_passed + 1;
    else
        fprintf('✗ 单元素相同测试失败\n');
    end
    
    if abs(od_single_diff - 0.0) < 1e-6
        fprintf('✓ 单元素不同测试通过\n');
        boundary_tests_passed = boundary_tests_passed + 1;
    else
        fprintf('✗ 单元素不同测试失败\n');
    end
    
    %% 测试总结
    fprintf('\n\n=== 测试总结 ===\n');
    fprintf('边界情况测试: %d/%d 通过\n', boundary_tests_passed, total_boundary_tests);
    
    if boundary_tests_passed == total_boundary_tests
        fprintf('✓ 所有边界情况测试通过\n');
    else
        fprintf('✗ 部分边界情况测试失败，需要检查实现\n');
    end
    
    fprintf('\n核心改进验证:\n');
    fprintf('1. 总不一致度模型: 已实现 OD_weighted = 1 - Total_Disagreement\n');
    fprintf('2. 二元不一致度: 已实现 Disagreement_d ∈ {0,1}\n');
    fprintf('3. 位置权重分配: 已实现基于OWA的位置重要性权重\n');
    fprintf('4. 长度惩罚机制: 已实现L_long和长度差异处理\n');
    fprintf('5. 顶部偏置效果: 通过orness参数控制顶部敏感性\n');
    
    fprintf('\n=== TW-dRPS算法验证完成 ===\n');
end

%% ========================================================================
%% 核心算法函数
%% ========================================================================

%% RPS创建和管理函数
function rps = create_rps(elements, pmf_map)
    rps.elements = elements;
    rps.pes = generate_pes(elements);
    rps.pmf_map = pmf_map;
    rps.pmf_vector = create_pmf_vector(rps.pes, pmf_map);
    
    % 验证PMF归一化
    if abs(sum(rps.pmf_vector) - 1.0) > 1e-10
        warning('PMF does not sum to 1: sum = %.10f', sum(rps.pmf_vector));
    end
end

function pes = generate_pes(elements)
    n = length(elements);
    pes = {};
    
    % 添加空集
    pes{end+1} = {};
    
    % 生成所有可能的排列事件
    for i = 1:n
        combinations = nchoosek(1:n, i);
        for c = 1:size(combinations, 1)
            selected_indices = combinations(c, :);
            selected_elements = elements(selected_indices);
            
            if i == 1
                % 单元素，直接添加
                pes{end+1} = selected_elements;
            else
                % 多元素，生成所有排列
                perm_indices = perms(1:i);
                for p = 1:size(perm_indices, 1)
                    perm_elements = selected_elements(perm_indices(p, :));
                    pes{end+1} = perm_elements;
                end
            end
        end
    end
    
    % 去重
    pes = unique_permutations(pes);
end

function unique_pes = unique_permutations(pes)
    unique_pes = {};
    seen = containers.Map();
    
    for i = 1:length(pes)
        perm_str = cell2str(pes{i});
        if ~isKey(seen, perm_str)
            seen(perm_str) = true;
            unique_pes{end+1} = pes{i};
        end
    end
end

function str = cell2str(cell_array)
    if isempty(cell_array)
        str = 'empty';
    else
        str = strjoin(cell_array, ',');
    end
end

function pmf_vector = create_pmf_vector(pes, pmf_map)
    pmf_vector = zeros(length(pes), 1);
    for i = 1:length(pes)
        perm_str = cell2str(pes{i});
        if isKey(pmf_map, perm_str)
            pmf_vector(i) = pmf_map(perm_str);
        end
    end
end

%% 距离计算函数
function distance = calculate_rps_distance(rps1, rps2, method, orness)
    if length(rps1.pes) ~= length(rps2.pes)
        error('RPS must have the same PES');
    end
    
    rd_matrix = calculate_rd_matrix(rps1.pes, method, orness);
    diff_vector = rps1.pmf_vector - rps2.pmf_vector;
    distance = sqrt(0.5 * diff_vector' * rd_matrix * diff_vector);
end

function rd_matrix = calculate_rd_matrix(pes, method, orness)
    n = length(pes);
    rd_matrix = zeros(n, n);
    
    for i = 1:n
        for j = 1:n
            A = pes{i};
            B = pes{j};
            
            % 计算Jaccard系数
            jaccard_coeff = calculate_jaccard_coefficient(A, B);
            
            % 计算有序度
            if strcmp(method, 'improved')
                od = calculate_improved_od(A, B, orness);
            else
                od = calculate_original_od(A, B);
            end
            
            % RD矩阵元素
            rd_matrix(i, j) = jaccard_coeff * od;
        end
    end
end

function jaccard = calculate_jaccard_coefficient(A, B)
    % 计算Jaccard系数: |A∩B| / |A∪B|
    if isempty(A) && isempty(B)
        jaccard = 1;  % 两个空集的Jaccard系数为1
        return;
    end
    
    if isempty(A) || isempty(B)
        jaccard = 0;  % 一个为空，一个非空
        return;
    end
    
    set_A = unique(A);
    set_B = unique(B);
    intersection = intersect(set_A, set_B);
    union_set = union(set_A, set_B);
    
    jaccard = length(intersection) / length(union_set);
end

%% 原始有序度计算（用于对比）
function od = calculate_original_od(A, B)
    if isempty(A) || isempty(B)
        od = 1;
        return;
    end
    
    common_elements = intersect(A, B);
    if isempty(common_elements)
        od = 1;
        return;
    end
    
    total_deviation = 0;
    for elem = common_elements
        pos_A = find(strcmp(A, elem), 1);
        pos_B = find(strcmp(B, elem), 1);
        if ~isempty(pos_A) && ~isempty(pos_B)
            total_deviation = total_deviation + abs(pos_A - pos_B);
        end
    end
    
    union_size = length(union(A, B));
    if union_size == 0
        od = 1;
    else
        od = exp(-total_deviation / union_size);
    end
end

%% 改进的有序度计算 - 符合TW-dRPS方案
function od = calculate_improved_od(A, B, orness)
    % 实现: OD_weighted(A, B) = 1 - Total_Disagreement(A, B)
    
    if isempty(A) && isempty(B)
        od = 1;  % 两个空排列完全一致
        return;
    end
    
    % 确保A和B是cell数组
    if ~iscell(A), A = {A}; end
    if ~iscell(B), B = {B}; end
    
    % 计算L_long：两个排列中较长的长度
    L_long = max(length(A), length(B));
    
    if L_long == 0
        od = 1;
        return;
    end
    
    % 生成位置权重：w_d描述位置d的重要性
    weights = generate_owa_weights(L_long, orness);
    
    % 计算总不一致度：Total_Disagreement = Σ_{d=1 to L_long} [w_d * Disagreement_d]
    total_disagreement = 0;
    
    for d = 1:L_long
        % 计算位置d的不一致度
        disagreement_d = calculate_position_disagreement(A, B, d);
        
        % 加权累加
        total_disagreement = total_disagreement + weights(d) * disagreement_d;
    end
    
    % 计算改进的有序度
    od = 1 - total_disagreement;
    
    % 确保od在[0,1]范围内
    od = max(0, min(1, od));
end

function disagreement = calculate_position_disagreement(A, B, position)
    % 计算位置position的不一致度
    % Disagreement_d = 0 如果A和B在位置d的元素相同
    % Disagreement_d = 1 如果不相同，或者其中一个排列在该位置已无元素
    
    % 检查位置是否超出排列长度
    has_A = position <= length(A);
    has_B = position <= length(B);
    
    if ~has_A && ~has_B
        % 两个排列都没有该位置的元素
        disagreement = 0;
    elseif ~has_A || ~has_B
        % 其中一个排列没有该位置的元素（长度惩罚）
        disagreement = 1;
    else
        % 两个排列都有该位置的元素，比较是否相同
        if strcmp(A{position}, B{position})
            disagreement = 0;  % 元素相同
        else
            disagreement = 1;  % 元素不同
        end
    end
end

%% 改进的有序度计算 - 带详细步骤展示
function od = calculate_improved_od_detailed(A, B, orness)
    fprintf('  L_long = max(length(A), length(B)) = max(%d, %d) = %d\n', ...
        length(A), length(B), max(length(A), length(B)));
    
    L_long = max(length(A), length(B));
    
    if L_long == 0
        od = 1;
        fprintf('  两个排列都为空，OD = 1\n');
        return;
    end
    
    % 生成权重
    weights = generate_owa_weights(L_long, orness);
    fprintf('  位置权重 w = [');
    for i = 1:L_long
        fprintf('%.4f', weights(i));
        if i < L_long, fprintf(', '); end
    end
    fprintf(']\n');
    
    % 计算每个位置的不一致度
    fprintf('  位置不一致度计算:\n');
    total_disagreement = 0;
    
    for d = 1:L_long
        disagreement_d = calculate_position_disagreement(A, B, d);
        weighted_contribution = weights(d) * disagreement_d;
        total_disagreement = total_disagreement + weighted_contribution;
        
        % 显示详细信息
        elem_A = '';
        elem_B = '';
        if d <= length(A), elem_A = A{d}; end
        if d <= length(B), elem_B = B{d}; end
        
        fprintf('    位置%d: A[%d]=%s, B[%d]=%s, Disagreement=%d, w_%d=%.4f, 贡献=%.4f\n', ...
            d, d, elem_A, d, elem_B, disagreement_d, d, weights(d), weighted_contribution);
    end
    
    fprintf('  Total_Disagreement = %.6f\n', total_disagreement);
    
    od = 1 - total_disagreement;
    od = max(0, min(1, od));
    
    fprintf('  OD_weighted = 1 - Total_Disagreement = 1 - %.6f = %.6f\n', ...
        total_disagreement, od);
end

%% OWA权重生成函数 - 高精度实现
function weights = generate_owa_weights(n, orness)
    % 基于Orness和最大熵原理生成OWA权重
    
    if n == 1
        weights = 1;
        return;
    end
    
    % 验证Orness范围
    if orness < 0 || orness > 1
        error('Orness must be in [0, 1]');
    end
    
    % 特殊情况处理
    if abs(orness - 0.5) < 1e-12
        % 中性权重：均匀分布
        weights = ones(n, 1) / n;
        return;
    elseif abs(orness - 1) < 1e-12
        % 完全乐观：第一个位置权重为1
        weights = zeros(n, 1);
        weights(1) = 1;
        return;
    elseif abs(orness) < 1e-12
        % 完全悲观：最后一个位置权重为1
        weights = zeros(n, 1);
        weights(n) = 1;
        return;
    end
    
    % 使用数值方法求解最优权重
    alpha = calculate_alpha_from_orness(orness, n);
    weights = zeros(n, 1);
    
    for i = 1:n
        weights(i) = exp(-alpha * (i - 1));
    end
    
    % 归一化
    weights = weights / sum(weights);
    
    % 微调以满足精确的orness约束
    weights = fine_tune_weights(weights, orness, n);
    
    % 最终验证
    calculated_orness = calculate_orness_from_weights(weights);
    if abs(calculated_orness - orness) > 1e-4
        warning('Generated weights may not satisfy exact orness constraint. Target: %.6f, Actual: %.6f', ...
            orness, calculated_orness);
    end
end

function alpha = calculate_alpha_from_orness(orness, n)
    % 使用二分法求解alpha参数
    
    if abs(orness - 0.5) < 1e-12
        alpha = 0;
        return;
    end
    
    alpha_min = 0;
    alpha_max = 50;
    tolerance = 1e-12;
    max_iterations = 500;
    
    for iter = 1:max_iterations
        alpha_mid = (alpha_min + alpha_max) / 2;
        
        % 计算当前alpha对应的权重
        weights_temp = zeros(n, 1);
        for i = 1:n
            weights_temp(i) = exp(-alpha_mid * (i - 1));
        end
        weights_temp = weights_temp / sum(weights_temp);
        
        % 计算当前orness
        orness_temp = calculate_orness_from_weights(weights_temp);
        
        if abs(orness_temp - orness) < tolerance
            alpha = alpha_mid;
            return;
        end
        
        if orness_temp > orness
            alpha_max = alpha_mid;
        else
            alpha_min = alpha_mid;
        end
        
        if (alpha_max - alpha_min) < 1e-15
            break;
        end
    end
    
    alpha = (alpha_min + alpha_max) / 2;
end

function orness = calculate_orness_from_weights(weights)
    % 根据权重计算orness值
    n = length(weights);
    
    if n == 1
        orness = 0.5;
        return;
    end
    
    orness = 0;
    for i = 1:n
        orness = orness + (n - i) * weights(i);
    end
    orness = orness / (n - 1);
end

function weights = fine_tune_weights(initial_weights, target_orness, n)
    % 微调权重以满足精确的orness约束
    
    weights = initial_weights;
    max_iterations = 200;
    tolerance = 1e-12;
    
    for iter = 1:max_iterations
        current_orness = calculate_orness_from_weights(weights);
        error = current_orness - target_orness;
        
        if abs(error) < tolerance
            break;
        end
        
        % 计算梯度
        jacobian = zeros(n, 1);
        for i = 1:n
            jacobian(i) = (n - i) / (n - 1);
        end
        
        % 更新权重
        step_size = error / (jacobian' * jacobian + 1e-12);
        weights = weights - step_size * jacobian;
        
        % 确保权重非负且归一化
        weights = max(weights, 1e-15);
        weights = weights / sum(weights);
    end
end

%% 熵计算函数
function entropy = calculate_rps_entropy(pmf_vector)
    entropy = 0;
    for i = 1:length(pmf_vector)
        if pmf_vector(i) > 0
            entropy = entropy - pmf_vector(i) * log(pmf_vector(i));
        end
    end
end

%% 显示和分析函数
function display_rps_detailed(rps, name)
    fprintf('\n%s:\n', name);
    fprintf('主要排列事件:\n');
    for i = 1:length(rps.pmf_vector)
        if rps.pmf_vector(i) > 0
            perm_str = cell2str(rps.pes{i});
            fprintf('  <%s>: %.3f\n', perm_str, rps.pmf_vector(i));
        end
    end
end

function event_idx = find_event_index(pes, event_str)
    % 在PES中查找特定排列事件的索引
    event_idx = [];
    for i = 1:length(pes)
        if strcmp(cell2str(pes{i}), event_str)
            event_idx = i;
            return;
        end
    end
end

function create_fusion_comparison_table(rps1, optimal_pmf_orig, optimal_pmf_impr, ...
    min_entropy_orig, min_entropy_impr, idx1_orig, idx2_orig, idx1_impr, idx2_impr)
    
    fprintf('\n=== 融合结果对比表格 ===\n');
    fprintf('融合方法                    主要排列事件概率                     熵值\n');
    fprintf('                     A,B,C   B,A,C   C,A,B   A,C,B   其他\n');
    fprintf('----------------------------------------------------------------\n');
    
    % 定义主要排列事件
    main_events = {'A,B,C', 'B,A,C', 'C,A,B', 'A,C,B'};
    
    % 显示原始方法结果
    fprintf('原始方法 (RPS%d⊕RPS%d)    ', idx1_orig, idx2_orig);
    other_prob_orig = 1;
    for event = main_events
        event_idx = find_event_index(rps1.pes, event{1});
        if ~isempty(event_idx) && event_idx <= length(optimal_pmf_orig)
            prob = optimal_pmf_orig(event_idx);
            fprintf('%.4f  ', prob);
            other_prob_orig = other_prob_orig - prob;
        else
            fprintf('0.0000  ');
        end
    end
    other_prob_orig = max(0, other_prob_orig);
    fprintf('%.4f  %.6f\n', other_prob_orig, min_entropy_orig);
    
    % 显示改进方法结果
    fprintf('改进方法 (RPS%d⊕RPS%d)    ', idx1_impr, idx2_impr);
    other_prob_impr = 1;
    for event = main_events
        event_idx = find_event_index(rps1.pes, event{1});
        if ~isempty(event_idx) && event_idx <= length(optimal_pmf_impr)
            prob = optimal_pmf_impr(event_idx);
            fprintf('%.4f  ', prob);
            other_prob_impr = other_prob_impr - prob;
        else
            fprintf('0.0000  ');
        end
    end
    other_prob_impr = max(0, other_prob_impr);
    fprintf('%.4f  %.6f\n', other_prob_impr, min_entropy_impr);
    
    % 显示差异
    fprintf('差异 (改进-原始)         ');
    for event = main_events
        event_idx = find_event_index(rps1.pes, event{1});
        if ~isempty(event_idx) && event_idx <= length(optimal_pmf_orig) && event_idx <= length(optimal_pmf_impr)
            diff = optimal_pmf_impr(event_idx) - optimal_pmf_orig(event_idx);
            fprintf('%+.4f  ', diff);
        else
            fprintf('+0.0000 ');
        end
    end
    other_diff = other_prob_impr - other_prob_orig;
    entropy_diff = min_entropy_impr - min_entropy_orig;
    fprintf('%+.4f  %+.6f\n', other_diff, entropy_diff);
    
    fprintf('----------------------------------------------------------------\n');
end

function analyze_results(optimal_pmf_orig, optimal_pmf_impr, min_entropy_orig, min_entropy_impr, ...
    min_idx_orig, min_idx_impr, combinations_2)
    
    fprintf('=== 结果分析 ===\n');
    
    % 分析信息质量变化
    entropy_diff = min_entropy_impr - min_entropy_orig;
    fprintf('\n1. 信息质量变化:\n');
    if entropy_diff < -0.001
        fprintf('   熵值减少 %.6f，信息质量提升 %.2f%%\n', -entropy_diff, -entropy_diff/min_entropy_orig*100);
    elseif entropy_diff > 0.001
        fprintf('   熵值增加 %.6f，信息质量下降 %.2f%%\n', entropy_diff, entropy_diff/min_entropy_orig*100);
    else
        fprintf('   熵值基本不变，信息质量保持稳定\n');
    end
    
    % 分析最优组合变化
    fprintf('\n2. 最优组合选择:\n');
    if min_idx_orig == min_idx_impr
        fprintf('   两种方法选择了相同的最优组合 (RPS%d+RPS%d)\n', ...
            combinations_2(min_idx_orig, 1), combinations_2(min_idx_orig, 2));
        fprintf('   说明改进主要体现在权重分配和距离计算上\n');
    else
        fprintf('   原始方法选择: RPS%d+RPS%d\n', ...
            combinations_2(min_idx_orig, 1), combinations_2(min_idx_orig, 2));
        fprintf('   改进方法选择: RPS%d+RPS%d\n', ...
            combinations_2(min_idx_impr, 1), combinations_2(min_idx_impr, 2));
        fprintf('   改进方法改变了最优组合的选择\n');
    end
    
    % 分析概率分布变化
    fprintf('\n3. 概率分布变化分析:\n');
    max_diff = max(abs(optimal_pmf_impr - optimal_pmf_orig));
    avg_diff = mean(abs(optimal_pmf_impr - optimal_pmf_orig));
    fprintf('   最大概率变化: %.6f\n', max_diff);
    fprintf('   平均概率变化: %.6f\n', avg_diff);
    
    if max_diff > 0.01
        fprintf('   改进方法显著改变了概率分布\n');
    elseif max_diff > 0.001
        fprintf('   改进方法适度调整了概率分布\n');
    else
        fprintf('   改进方法对概率分布影响较小\n');
    end
    
    fprintf('\n4. TW-dRPS改进效果总结:\n');
    fprintf('   - 顶部偏置权重成功应用于距离计算\n');
    fprintf('   - 总不一致度模型替代了原始指数模型\n');
    fprintf('   - 位置敏感性增强了排序差异的识别能力\n');
    
    if entropy_diff < 0
        fprintf('   - 信息质量得到提升，验证了改进方法的有效性\n');
    end
end