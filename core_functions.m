%% 核心函数定义 - 完全符合TW-dRPS方案要求

%% RPS创建和管理函数
function rps = create_rps(elements, pmf_map)
    rps.elements = elements;
    rps.pes = generate_pes(elements);
    rps.pmf_map = pmf_map;
    rps.pmf_vector = create_pmf_vector(rps.pes, pmf_map);
    
    % 验证PMF归一化
    if abs(sum(rps.pmf_vector) - 1.0) > 1e-10
        warning('PMF does not sum to 1: sum = %.10f', sum(rps.pmf_vector));
    end
end

function pes = generate_pes(elements)
    n = length(elements);
    pes = {};
    
    % 添加空集
    pes{end+1} = {};
    
    % 生成所有可能的排列事件
    for i = 1:n
        combinations = nchoosek(1:n, i);
        for c = 1:size(combinations, 1)
            selected_indices = combinations(c, :);
            selected_elements = elements(selected_indices);
            
            if i == 1
                % 单元素，直接添加
                pes{end+1} = selected_elements;
            else
                % 多元素，生成所有排列
                perm_indices = perms(1:i);
                for p = 1:size(perm_indices, 1)
                    perm_elements = selected_elements(perm_indices(p, :));
                    pes{end+1} = perm_elements;
                end
            end
        end
    end
    
    % 去重
    pes = unique_permutations(pes);
end

function unique_pes = unique_permutations(pes)
    unique_pes = {};
    seen = containers.Map();
    
    for i = 1:length(pes)
        perm_str = cell2str(pes{i});
        if ~isKey(seen, perm_str)
            seen(perm_str) = true;
            unique_pes{end+1} = pes{i};
        end
    end
end

function str = cell2str(cell_array)
    if isempty(cell_array)
        str = 'empty';
    else
        str = strjoin(cell_array, ',');
    end
end

function pmf_vector = create_pmf_vector(pes, pmf_map)
    pmf_vector = zeros(length(pes), 1);
    for i = 1:length(pes)
        perm_str = cell2str(pes{i});
        if isKey(pmf_map, perm_str)
            pmf_vector(i) = pmf_map(perm_str);
        end
    end
end

%% 距离计算函数
function distance = calculate_rps_distance(rps1, rps2, method, orness)
    if length(rps1.pes) ~= length(rps2.pes)
        error('RPS must have the same PES');
    end
    
    rd_matrix = calculate_rd_matrix(rps1.pes, method, orness);
    diff_vector = rps1.pmf_vector - rps2.pmf_vector;
    distance = sqrt(0.5 * diff_vector' * rd_matrix * diff_vector);
end

function rd_matrix = calculate_rd_matrix(pes, method, orness)
    n = length(pes);
    rd_matrix = zeros(n, n);
    
    for i = 1:n
        for j = 1:n
            A = pes{i};
            B = pes{j};
            
            % 计算Jaccard系数
            jaccard_coeff = calculate_jaccard_coefficient(A, B);
            
            % 计算有序度
            if strcmp(method, 'improved')
                od = calculate_improved_od(A, B, orness);
            else
                od = calculate_original_od(A, B);
            end
            
            % RD矩阵元素
            rd_matrix(i, j) = jaccard_coeff * od;
        end
    end
end

function jaccard = calculate_jaccard_coefficient(A, B)
    % 计算Jaccard系数: |A∩B| / |A∪B|
    if isempty(A) && isempty(B)
        jaccard = 1;  % 两个空集的Jaccard系数为1
        return;
    end
    
    if isempty(A) || isempty(B)
        jaccard = 0;  % 一个为空，一个非空
        return;
    end
    
    set_A = unique(A);
    set_B = unique(B);
    intersection = intersect(set_A, set_B);
    union_set = union(set_A, set_B);
    
    jaccard = length(intersection) / length(union_set);
end

%% 原始有序度计算（用于对比）
function od = calculate_original_od(A, B)
    if isempty(A) || isempty(B)
        od = 1;
        return;
    end
    
    common_elements = intersect(A, B);
    if isempty(common_elements)
        od = 1;
        return;
    end
    
    total_deviation = 0;
    for elem = common_elements
        pos_A = find(strcmp(A, elem), 1);
        pos_B = find(strcmp(B, elem), 1);
        if ~isempty(pos_A) && ~isempty(pos_B)
            total_deviation = total_deviation + abs(pos_A - pos_B);
        end
    end
    
    union_size = length(union(A, B));
    if union_size == 0
        od = 1;
    else
        od = exp(-total_deviation / union_size);
    end
end

%% 改进的有序度计算 - 符合TW-dRPS方案
function od = calculate_improved_od(A, B, orness)
    % 实现: OD_weighted(A, B) = 1 - Total_Disagreement(A, B)
    
    if isempty(A) && isempty(B)
        od = 1;  % 两个空排列完全一致
        return;
    end
    
    % 确保A和B是cell数组
    if ~iscell(A), A = {A}; end
    if ~iscell(B), B = {B}; end
    
    % 计算L_long：两个排列中较长的长度
    L_long = max(length(A), length(B));
    
    if L_long == 0
        od = 1;
        return;
    end
    
    % 生成位置权重：w_d描述位置d的重要性
    weights = generate_owa_weights(L_long, orness);
    
    % 计算总不一致度：Total_Disagreement = Σ_{d=1 to L_long} [w_d * Disagreement_d]
    total_disagreement = 0;
    
    for d = 1:L_long
        % 计算位置d的不一致度
        disagreement_d = calculate_position_disagreement(A, B, d);
        
        % 加权累加
        total_disagreement = total_disagreement + weights(d) * disagreement_d;
    end
    
    % 计算改进的有序度
    od = 1 - total_disagreement;
    
    % 确保od在[0,1]范围内
    od = max(0, min(1, od));
end

function disagreement = calculate_position_disagreement(A, B, position)
    % 计算位置position的不一致度
    % Disagreement_d = 0 如果A和B在位置d的元素相同
    % Disagreement_d = 1 如果不相同，或者其中一个排列在该位置已无元素
    
    % 检查位置是否超出排列长度
    has_A = position <= length(A);
    has_B = position <= length(B);
    
    if ~has_A && ~has_B
        % 两个排列都没有该位置的元素
        disagreement = 0;
    elseif ~has_A || ~has_B
        % 其中一个排列没有该位置的元素（长度惩罚）
        disagreement = 1;
    else
        % 两个排列都有该位置的元素，比较是否相同
        if strcmp(A{position}, B{position})
            disagreement = 0;  % 元素相同
        else
            disagreement = 1;  % 元素不同
        end
    end
end

%% 改进的有序度计算 - 带详细步骤展示
function od = calculate_improved_od_detailed(A, B, orness)
    fprintf('  L_long = max(length(A), length(B)) = max(%d, %d) = %d\n', ...
        length(A), length(B), max(length(A), length(B)));
    
    L_long = max(length(A), length(B));
    
    if L_long == 0
        od = 1;
        fprintf('  两个排列都为空，OD = 1\n');
        return;
    end
    
    % 生成权重
    weights = generate_owa_weights(L_long, orness);
    fprintf('  位置权重 w = [');
    for i = 1:L_long
        fprintf('%.4f', weights(i));
        if i < L_long, fprintf(', '); end
    end
    fprintf(']\n');
    
    % 计算每个位置的不一致度
    fprintf('  位置不一致度计算:\n');
    total_disagreement = 0;
    
    for d = 1:L_long
        disagreement_d = calculate_position_disagreement(A, B, d);
        weighted_contribution = weights(d) * disagreement_d;
        total_disagreement = total_disagreement + weighted_contribution;
        
        % 显示详细信息
        elem_A = '';
        elem_B = '';
        if d <= length(A), elem_A = A{d}; end
        if d <= length(B), elem_B = B{d}; end
        
        fprintf('    位置%d: A[%d]=%s, B[%d]=%s, Disagreement=%d, w_%d=%.4f, 贡献=%.4f\n', ...
            d, d, elem_A, d, elem_B, disagreement_d, d, weights(d), weighted_contribution);
    end
    
    fprintf('  Total_Disagreement = %.6f\n', total_disagreement);
    
    od = 1 - total_disagreement;
    od = max(0, min(1, od));
    
    fprintf('  OD_weighted = 1 - Total_Disagreement = 1 - %.6f = %.6f\n', ...
        total_disagreement, od);
end

%% OWA权重生成函数 - 高精度实现
function weights = generate_owa_weights(n, orness)
    % 基于Orness和最大熵原理生成OWA权重
    
    if n == 1
        weights = 1;
        return;
    end
    
    % 验证Orness范围
    if orness < 0 || orness > 1
        error('Orness must be in [0, 1]');
    end
    
    % 特殊情况处理
    if abs(orness - 0.5) < 1e-12
        % 中性权重：均匀分布
        weights = ones(n, 1) / n;
        return;
    elseif abs(orness - 1) < 1e-12
        % 完全乐观：第一个位置权重为1
        weights = zeros(n, 1);
        weights(1) = 1;
        return;
    elseif abs(orness) < 1e-12
        % 完全悲观：最后一个位置权重为1
        weights = zeros(n, 1);
        weights(n) = 1;
        return;
    end
    
    % 使用数值方法求解最优权重
    alpha = calculate_alpha_from_orness(orness, n);
    weights = zeros(n, 1);
    
    for i = 1:n
        weights(i) = exp(-alpha * (i - 1));
    end
    
    % 归一化
    weights = weights / sum(weights);
    
    % 微调以满足精确的orness约束
    weights = fine_tune_weights(weights, orness, n);
    
    % 最终验证
    calculated_orness = calculate_orness_from_weights(weights);
    if abs(calculated_orness - orness) > 1e-4
        warning('Generated weights may not satisfy exact orness constraint. Target: %.6f, Actual: %.6f', ...
            orness, calculated_orness);
    end
end

function alpha = calculate_alpha_from_orness(orness, n)
    % 使用二分法求解alpha参数
    
    if abs(orness - 0.5) < 1e-12
        alpha = 0;
        return;
    end
    
    alpha_min = 0;
    alpha_max = 50;
    tolerance = 1e-12;
    max_iterations = 500;
    
    for iter = 1:max_iterations
        alpha_mid = (alpha_min + alpha_max) / 2;
        
        % 计算当前alpha对应的权重
        weights_temp = zeros(n, 1);
        for i = 1:n
            weights_temp(i) = exp(-alpha_mid * (i - 1));
        end
        weights_temp = weights_temp / sum(weights_temp);
        
        % 计算当前orness
        orness_temp = calculate_orness_from_weights(weights_temp);
        
        if abs(orness_temp - orness) < tolerance
            alpha = alpha_mid;
            return;
        end
        
        if orness_temp > orness
            alpha_max = alpha_mid;
        else
            alpha_min = alpha_mid;
        end
        
        if (alpha_max - alpha_min) < 1e-15
            break;
        end
    end
    
    alpha = (alpha_min + alpha_max) / 2;
end

function orness = calculate_orness_from_weights(weights)
    % 根据权重计算orness值
    n = length(weights);
    
    if n == 1
        orness = 0.5;
        return;
    end
    
    orness = 0;
    for i = 1:n
        orness = orness + (n - i) * weights(i);
    end
    orness = orness / (n - 1);
end

function weights = fine_tune_weights(initial_weights, target_orness, n)
    % 微调权重以满足精确的orness约束
    
    weights = initial_weights;
    max_iterations = 200;
    tolerance = 1e-12;
    
    for iter = 1:max_iterations
        current_orness = calculate_orness_from_weights(weights);
        error = current_orness - target_orness;
        
        if abs(error) < tolerance
            break;
        end
        
        % 计算梯度
        jacobian = zeros(n, 1);
        for i = 1:n
            jacobian(i) = (n - i) / (n - 1);
        end
        
        % 更新权重
        step_size = error / (jacobian' * jacobian + 1e-12);
        weights = weights - step_size * jacobian;
        
        % 确保权重非负且归一化
        weights = max(weights, 1e-15);
        weights = weights / sum(weights);
    end
end

%% 熵计算函数
function entropy = calculate_rps_entropy(pmf_vector)
    entropy = 0;
    for i = 1:length(pmf_vector)
        if pmf_vector(i) > 0
            entropy = entropy - pmf_vector(i) * log(pmf_vector(i));
        end
    end
end

%% 显示和分析函数
function display_rps_detailed(rps, name)
    fprintf('\n%s:\n', name);
    fprintf('主要排列事件:\n');
    for i = 1:length(rps.pmf_vector)
        if rps.pmf_vector(i) > 0
            perm_str = cell2str(rps.pes{i});
            fprintf('  <%s>: %.3f\n', perm_str, rps.pmf_vector(i));
        end
    end
end

function event_idx = find_event_index(pes, event_str)
    % 在PES中查找特定排列事件的索引
    event_idx = [];
    for i = 1:length(pes)
        if strcmp(cell2str(pes{i}), event_str)
            event_idx = i;
            return;
        end
    end
end

function create_fusion_comparison_table(rps1, optimal_pmf_orig, optimal_pmf_impr, ...
    min_entropy_orig, min_entropy_impr, idx1_orig, idx2_orig, idx1_impr, idx2_impr)
    
    fprintf('\n=== 融合结果对比表格 ===\n');
    fprintf('融合方法                    主要排列事件概率                     熵值\n');
    fprintf('                     A,B,C   B,A,C   C,A,B   A,C,B   其他\n');
    fprintf('----------------------------------------------------------------\n');
    
    % 定义主要排列事件
    main_events = {'A,B,C', 'B,A,C', 'C,A,B', 'A,C,B'};
    
    % 显示原始方法结果
    fprintf('原始方法 (RPS%d⊕RPS%d)    ', idx1_orig, idx2_orig);
    other_prob_orig = 1;
    for event = main_events
        event_idx = find_event_index(rps1.pes, event{1});
        if ~isempty(event_idx) && event_idx <= length(optimal_pmf_orig)
            prob = optimal_pmf_orig(event_idx);
            fprintf('%.4f  ', prob);
            other_prob_orig = other_prob_orig - prob;
        else
            fprintf('0.0000  ');
        end
    end
    other_prob_orig = max(0, other_prob_orig);
    fprintf('%.4f  %.6f\n', other_prob_orig, min_entropy_orig);
    
    % 显示改进方法结果
    fprintf('改进方法 (RPS%d⊕RPS%d)    ', idx1_impr, idx2_impr);
    other_prob_impr = 1;
    for event = main_events
        event_idx = find_event_index(rps1.pes, event{1});
        if ~isempty(event_idx) && event_idx <= length(optimal_pmf_impr)
            prob = optimal_pmf_impr(event_idx);
            fprintf('%.4f  ', prob);
            other_prob_impr = other_prob_impr - prob;
        else
            fprintf('0.0000  ');
        end
    end
    other_prob_impr = max(0, other_prob_impr);
    fprintf('%.4f  %.6f\n', other_prob_impr, min_entropy_impr);
    
    % 显示差异
    fprintf('差异 (改进-原始)         ');
    for event = main_events
        event_idx = find_event_index(rps1.pes, event{1});
        if ~isempty(event_idx) && event_idx <= length(optimal_pmf_orig) && event_idx <= length(optimal_pmf_impr)
            diff = optimal_pmf_impr(event_idx) - optimal_pmf_orig(event_idx);
            fprintf('%+.4f  ', diff);
        else
            fprintf('+0.0000 ');
        end
    end
    other_diff = other_prob_impr - other_prob_orig;
    entropy_diff = min_entropy_impr - min_entropy_orig;
    fprintf('%+.4f  %+.6f\n', other_diff, entropy_diff);
    
    fprintf('----------------------------------------------------------------\n');
end

function analyze_results(optimal_pmf_orig, optimal_pmf_impr, min_entropy_orig, min_entropy_impr, ...
    min_idx_orig, min_idx_impr, combinations_2)
    
    fprintf('=== 结果分析 ===\n');
    
    % 分析信息质量变化
    entropy_diff = min_entropy_impr - min_entropy_orig;
    fprintf('\n1. 信息质量变化:\n');
    if entropy_diff < -0.001
        fprintf('   熵值减少 %.6f，信息质量提升 %.2f%%\n', -entropy_diff, -entropy_diff/min_entropy_orig*100);
    elseif entropy_diff > 0.001
        fprintf('   熵值增加 %.6f，信息质量下降 %.2f%%\n', entropy_diff, entropy_diff/min_entropy_orig*100);
    else
        fprintf('   熵值基本不变，信息质量保持稳定\n');
    end
    
    % 分析最优组合变化
    fprintf('\n2. 最优组合选择:\n');
    if min_idx_orig == min_idx_impr
        fprintf('   两种方法选择了相同的最优组合 (RPS%d+RPS%d)\n', ...
            combinations_2(min_idx_orig, 1), combinations_2(min_idx_orig, 2));
        fprintf('   说明改进主要体现在权重分配和距离计算上\n');
    else
        fprintf('   原始方法选择: RPS%d+RPS%d\n', ...
            combinations_2(min_idx_orig, 1), combinations_2(min_idx_orig, 2));
        fprintf('   改进方法选择: RPS%d+RPS%d\n', ...
            combinations_2(min_idx_impr, 1), combinations_2(min_idx_impr, 2));
        fprintf('   改进方法改变了最优组合的选择\n');
    end
    
    % 分析概率分布变化
    fprintf('\n3. 概率分布变化分析:\n');
    max_diff = max(abs(optimal_pmf_impr - optimal_pmf_orig));
    avg_diff = mean(abs(optimal_pmf_impr - optimal_pmf_orig));
    fprintf('   最大概率变化: %.6f\n', max_diff);
    fprintf('   平均概率变化: %.6f\n', avg_diff);
    
    if max_diff > 0.01
        fprintf('   改进方法显著改变了概率分布\n');
    elseif max_diff > 0.001
        fprintf('   改进方法适度调整了概率分布\n');
    else
        fprintf('   改进方法对概率分布影响较小\n');
    end
    
    fprintf('\n4. TW-dRPS改进效果总结:\n');
    fprintf('   - 顶部偏置权重成功应用于距离计算\n');
    fprintf('   - 总不一致度模型替代了原始指数模型\n');
    fprintf('   - 位置敏感性增强了排序差异的识别能力\n');
    
    if entropy_diff < 0
        fprintf('   - 信息质量得到提升，验证了改进方法的有效性\n');
    end
end