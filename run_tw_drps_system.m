%% TW-dRPS系统运行脚本
% 完整的基于"总不一致度"模型的RPS距离计算和融合系统

clear; clc; close all;

fprintf('=== TW-dRPS完整系统演示 ===\n');
fprintf('基于顶部加权的随机排列集距离计算和融合系统\n');
fprintf('实现了完全符合方案要求的"总不一致度"模型\n\n');

%% 选择运行模式
fprintf('请选择运行模式:\n');
fprintf('1. 核心改进演示（快速了解改进效果）\n');
fprintf('2. 完整系统演示（详细的融合过程）\n');
fprintf('3. 算法验证测试（技术验证）\n');
fprintf('4. 全部运行\n');

choice = input('请输入选择 (1-4): ');

if isempty(choice) || choice < 1 || choice > 4
    choice = 1;
    fprintf('使用默认选择: 核心改进演示\n');
end

%% 运行选择的模式
switch choice
    case 1
        fprintf('\n正在运行核心改进演示...\n');
        fprintf('=====================================\n');
        run_core_demo();
        
    case 2
        fprintf('\n正在运行完整系统演示...\n');
        fprintf('=====================================\n');
        run_system_demo();
        
    case 3
        fprintf('\n正在运行算法验证测试...\n');
        fprintf('=====================================\n');
        run_algorithm_tests();
        
    case 4
        fprintf('\n正在运行核心改进演示...\n');
        fprintf('=====================================\n');
        run_core_demo();
        
        fprintf('\n\n正在运行完整系统演示...\n');
        fprintf('=====================================\n');
        run_system_demo();
        
        fprintf('\n\n正在运行算法验证测试...\n');
        fprintf('=====================================\n');
        run_algorithm_tests();
end

fprintf('\n=== TW-dRPS系统运行完成 ===\n');

%% 核心演示函数
function run_core_demo()
    % 运行核心改进演示
    try
        run('demo_core_improvement.m');
    catch ME
        fprintf('运行核心演示时出错: %s\n', ME.message);
        fprintf('请确保所有必要的函数文件都在当前目录中\n');
    end
end

%% 系统演示函数
function run_system_demo()
    % 运行完整的系统演示
    try
        run('improved_rps_distance_system.m');
    catch ME
        fprintf('运行系统演示时出错: %s\n', ME.message);
        fprintf('请确保所有必要的函数文件都在当前目录中\n');
    end
end

%% 算法测试函数
function run_algorithm_tests()
    % 运行算法验证测试
    try
        run('test_tw_drps.m');
    catch ME
        fprintf('运行算法测试时出错: %s\n', ME.message);
        fprintf('请确保所有必要的函数文件都在当前目录中\n');
    end
end