%% TW-dRPS核心改进演示
% 直观展示"总不一致度"模型相对于原始方法的改进效果

clear; clc;

% 加载核心函数
run('core_functions.m');

fprintf('=== TW-dRPS核心改进演示 ===\n');
fprintf('展示"总不一致度"模型的顶部偏置效果\n\n');

%% 核心问题演示
fprintf('问题描述:\n');
fprintf('原始dRPS方法的核心缺陷：第1位的交换与最后1位的交换被赋予同等惩罚\n');
fprintf('这在"冠军与亚军之争"远比"第99名与第100名之争"更重要的现实场景中是不合理的\n\n');

%% 测试用例设计
fprintf('测试用例设计:\n');
fprintf('===============\n');

% 基准排列
base = {'A', 'B', 'C', 'D'};

% 情况1：顶部交换（第1、2位）
top_swap = {'B', 'A', 'C', 'D'};

% 情况2：底部交换（第3、4位）
bottom_swap = {'A', 'B', 'D', 'C'};

% 情况3：中间交换（第2、3位）
middle_swap = {'A', 'C', 'B', 'D'};

fprintf('基准排列:   [%s]\n', strjoin(base, ', '));
fprintf('顶部交换:   [%s] (第1、2位交换)\n', strjoin(top_swap, ', '));
fprintf('中间交换:   [%s] (第2、3位交换)\n', strjoin(middle_swap, ', '));
fprintf('底部交换:   [%s] (第3、4位交换)\n', strjoin(bottom_swap, ', '));

%% 原始方法计算
fprintf('\n原始方法计算结果:\n');
fprintf('==================\n');

od_orig_top = calculate_original_od(base, top_swap);
od_orig_middle = calculate_original_od(base, middle_swap);
od_orig_bottom = calculate_original_od(base, bottom_swap);

fprintf('基准 vs 顶部交换:   OD = %.6f\n', od_orig_top);
fprintf('基准 vs 中间交换:   OD = %.6f\n', od_orig_middle);
fprintf('基准 vs 底部交换:   OD = %.6f\n', od_orig_bottom);

fprintf('\n原始方法分析:\n');
if abs(od_orig_top - od_orig_bottom) < 0.001
    fprintf('✗ 原始方法对顶部和底部交换给予相同惩罚 (差异: %.6f)\n', abs(od_orig_top - od_orig_bottom));
    fprintf('  这违背了"顶部更重要"的直觉\n');
end

%% 改进方法计算 - 不同Orness值
fprintf('\n改进方法计算结果:\n');
fprintf('==================\n');

orness_values = [0.5, 0.7, 0.9];

fprintf('Orness值  顶部交换    中间交换    底部交换    顶部vs底部差异\n');
fprintf('------------------------------------------------------------\n');

for orness = orness_values
    od_impr_top = calculate_improved_od(base, top_swap, orness);
    od_impr_middle = calculate_improved_od(base, middle_swap, orness);
    od_impr_bottom = calculate_improved_od(base, bottom_swap, orness);
    
    diff_top_bottom = od_impr_top - od_impr_bottom;
    
    fprintf('%.1f       %.6f    %.6f    %.6f    %+.6f\n', ...
        orness, od_impr_top, od_impr_middle, od_impr_bottom, diff_top_bottom);
end

%% 详细分析一个高Orness情况
fprintf('\n详细分析 (Orness = 0.9):\n');
fprintf('========================\n');

orness_high = 0.9;

fprintf('权重分布 (长度=4, Orness=%.1f):\n', orness_high);
weights = generate_owa_weights(4, orness_high);
fprintf('位置权重: [%.4f, %.4f, %.4f, %.4f]\n', weights(1), weights(2), weights(3), weights(4));
fprintf('解释: 第1位权重最大(%.4f)，第4位权重最小(%.4f)\n', weights(1), weights(4));

fprintf('\n顶部交换详细计算:\n');
od_top_detailed = calculate_improved_od_detailed(base, top_swap, orness_high);

fprintf('\n底部交换详细计算:\n');
od_bottom_detailed = calculate_improved_od_detailed(base, bottom_swap, orness_high);

%% 改进效果总结
fprintf('\n改进效果总结:\n');
fprintf('==============\n');

od_orig_diff = abs(od_orig_top - od_orig_bottom);
od_impr_diff = abs(calculate_improved_od(base, top_swap, 0.9) - calculate_improved_od(base, bottom_swap, 0.9));

fprintf('原始方法: 顶部vs底部交换差异 = %.6f\n', od_orig_diff);
fprintf('改进方法: 顶部vs底部交换差异 = %.6f\n', od_impr_diff);
fprintf('改进倍数: %.2fx\n', od_impr_diff / max(od_orig_diff, 1e-10));

if od_impr_diff > od_orig_diff * 2
    fprintf('✓ 改进方法显著增强了对位置重要性的感知能力\n');
elseif od_impr_diff > od_orig_diff
    fprintf('✓ 改进方法适度提升了对位置重要性的感知能力\n');
else
    fprintf('? 改进效果不明显，可能需要调整参数\n');
end

%% 实际应用场景示例
fprintf('\n实际应用场景示例:\n');
fprintf('==================\n');

fprintf('1. 搜索引擎结果排序:\n');
fprintf('   - 第1名与第2名的交换比第9名与第10名的交换更重要\n');
fprintf('   - 用户主要关注前几个搜索结果\n\n');

fprintf('2. 威胁等级评估:\n');
fprintf('   - 高威胁与中威胁的混淆比中威胁与低威胁的混淆更严重\n');
fprintf('   - 需要重点关注高风险项目的准确性\n\n');

fprintf('3. 专家意见融合:\n');
fprintf('   - 专家对重要问题的分歧需要更多关注\n');
fprintf('   - 顶部排序的一致性更为关键\n\n');

%% 参数选择指导
fprintf('参数选择指导:\n');
fprintf('==============\n');
fprintf('Orness = 0.5: 均匀权重，等同于原始方法\n');
fprintf('Orness = 0.7: 适度顶部偏置，适合一般应用\n');
fprintf('Orness = 0.9: 强顶部偏置，适合高度重视顶部的场景\n');
fprintf('Orness = 0.95: 极强顶部偏置，几乎只关注前几位\n\n');

fprintf('建议:\n');
fprintf('- 搜索引擎: Orness = 0.8-0.9\n');
fprintf('- 风险评估: Orness = 0.85-0.95\n');
fprintf('- 推荐系统: Orness = 0.7-0.8\n');
fprintf('- 一般排序: Orness = 0.6-0.7\n');

fprintf('\n=== 核心改进演示完成 ===\n');
fprintf('TW-dRPS成功解决了原始方法"位置等权"的核心缺陷\n');