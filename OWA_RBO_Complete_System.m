%% OWA-RBO Complete System - 基于OWA-RBO的RPS距离计算系统
% 核心流程: RPS输入 → dRPS距离对比 → 相似度矩阵 → 支持度 → 可信度
%
% 主要特性:
% 1. 灵活的RPS输入（可调节RPS数目和元素数目）
% 2. OWA-RBO距离计算与原论文dRPS对比
% 3. 完整的相似度计算流程
% 4. 严格按照算法实现，无简化

clear; clc;
fprintf('=== OWA-RBO Complete System ===\n');
fprintf('基于OWA-RBO的RPS距离计算和融合系统\n\n');

%% 步骤1: RPS输入设置
fprintf('步骤1: RPS输入设置\n');
fprintf('==================\n');

% 系统参数
orness_alpha = 0.8;  % OWA-RBO的Orness参数
fprintf('OWA-RBO Orness参数 α = %.2f\n', orness_alpha);

% 定义元素集合（可灵活修改）
elements = {'A', 'B', 'C', 'D'};  % 可修改元素数目
n_elements = length(elements);
fprintf('元素集合: [%s] (共%d个元素)\n', strjoin(elements, ', '), n_elements);

% 创建RPS（可灵活修改RPS数目）
% RPS1: A优先
rps1_pmf = containers.Map();
rps1_pmf('A,B,C') = 0.6;
rps1_pmf('A,C,B') = 0.3;
rps1_pmf('B,A,C') = 0.1;

% RPS2: B优先
rps2_pmf = containers.Map();
rps2_pmf('B,A,C') = 0.7;
rps2_pmf('B,C,A') = 0.2;
rps2_pmf('A,B,C') = 0.1;

% RPS3: C优先
rps3_pmf = containers.Map();
rps3_pmf('C,A,B') = 0.5;
rps3_pmf('C,B,A') = 0.3;
rps3_pmf('A,C,B') = 0.2;

% 可以添加更多RPS
% rps4_pmf = containers.Map();
% rps4_pmf('D,A,B') = 0.8;
% rps4_pmf('D,B,A') = 0.2;

% 创建RPS结构
rps1 = create_rps(elements, rps1_pmf);
rps2 = create_rps(elements, rps2_pmf);
rps3 = create_rps(elements, rps3_pmf);

% RPS列表（可灵活修改）
rps_list = {rps1, rps2, rps3};  % 可添加更多RPS
n_rps = length(rps_list);

% 显示RPS信息
for i = 1:n_rps
    display_rps(rps_list{i}, sprintf('RPS%d', i));
end

fprintf('总计: %d个RPS\n', n_rps);

%% 步骤2: dRPS距离矩阵对比
fprintf('\n步骤2: dRPS距离矩阵对比\n');
fprintf('=======================\n');

% 计算原论文dRPS距离矩阵
fprintf('原论文dRPS距离矩阵:\n');
distance_matrix_original = zeros(n_rps, n_rps);
for i = 1:n_rps
    for j = 1:n_rps
        if i ~= j
            distance_matrix_original(i,j) = calculate_original_drps_distance(rps_list{i}, rps_list{j});
        end
    end
end
disp(distance_matrix_original);

% 计算OWA-RBO距离矩阵
fprintf('OWA-RBO距离矩阵 (α = %.2f):\n', orness_alpha);
distance_matrix_owa_rbo = zeros(n_rps, n_rps);
for i = 1:n_rps
    for j = 1:n_rps
        if i ~= j
            distance_matrix_owa_rbo(i,j) = calculate_owa_rbo_distance(rps_list{i}, rps_list{j}, orness_alpha);
        end
    end
end
disp(distance_matrix_owa_rbo);

% 计算差异矩阵
fprintf('距离差异矩阵 (OWA-RBO - 原dRPS):\n');
difference_matrix = distance_matrix_owa_rbo - distance_matrix_original;
disp(difference_matrix);

%% 步骤3: 相似度矩阵(SIM)
fprintf('\n步骤3: 相似度矩阵(SIM)\n');
fprintf('=====================\n');

% 原论文相似度矩阵
sim_matrix_original = 1 - distance_matrix_original;
for i = 1:n_rps
    sim_matrix_original(i,i) = 1;
end

% OWA-RBO相似度矩阵
sim_matrix_owa_rbo = 1 - distance_matrix_owa_rbo;
for i = 1:n_rps
    sim_matrix_owa_rbo(i,i) = 1;
end

fprintf('原论文相似度矩阵:\n');
disp(sim_matrix_original);

fprintf('OWA-RBO相似度矩阵:\n');
disp(sim_matrix_owa_rbo);

%% 步骤4: 支持度(Sup)
fprintf('\n步骤4: 支持度(Sup)\n');
fprintf('==================\n');

% 计算支持度
support_original = zeros(n_rps, 1);
support_owa_rbo = zeros(n_rps, 1);

for i = 1:n_rps
    support_original(i) = sum(sim_matrix_original(i, :)) - 1;
    support_owa_rbo(i) = sum(sim_matrix_owa_rbo(i, :)) - 1;
end

fprintf('支持度对比:\n');
fprintf('RPS    原论文方法    OWA-RBO方法\n');
fprintf('--------------------------------\n');
for i = 1:n_rps
    fprintf('RPS%d   %.6f      %.6f\n', i, support_original(i), support_owa_rbo(i));
end

%% 步骤5: 可信度(Cre)
fprintf('\n步骤5: 可信度(Cre)\n');
fprintf('==================\n');

% 计算可信度
total_support_original = sum(support_original);
total_support_owa_rbo = sum(support_owa_rbo);
credibility_original = support_original / total_support_original;
credibility_owa_rbo = support_owa_rbo / total_support_owa_rbo;

fprintf('可信度对比:\n');
fprintf('RPS    原论文方法    OWA-RBO方法\n');
fprintf('--------------------------------\n');
for i = 1:n_rps
    fprintf('RPS%d   %.6f      %.6f\n', i, credibility_original(i), credibility_owa_rbo(i));
end

fprintf('\n可信度验证:\n');
fprintf('原论文方法: Σ Cre = %.6f\n', sum(credibility_original));
fprintf('OWA-RBO方法: Σ Cre = %.6f\n', sum(credibility_owa_rbo));

fprintf('\n=== OWA-RBO系统运行完成 ===\n');

%% ========================================================================
%% 核心算法函数
%% ========================================================================

function rps = create_rps(elements, pmf_map)
% 创建RPS结构
rps.elements = elements;
rps.pes = generate_pes(elements);
rps.pmf_map = pmf_map;
rps.pmf_vector = create_pmf_vector(rps.pes, pmf_map);
end

function pes = generate_pes(elements)
% 生成排列事件空间
n = length(elements);
pes = {};

% 添加空集
pes{end+1} = {};

% 生成所有排列
for i = 1:n
    combinations = nchoosek(1:n, i);
    for c = 1:size(combinations, 1)
        selected_elements = elements(combinations(c, :));
        if i == 1
            pes{end+1} = selected_elements;
        else
            perm_indices = perms(1:i);
            for p = 1:size(perm_indices, 1)
                pes{end+1} = selected_elements(perm_indices(p, :));
            end
        end
    end
end
end

function pmf_vector = create_pmf_vector(pes, pmf_map)
% 创建PMF向量
pmf_vector = zeros(length(pes), 1);
for i = 1:length(pes)
    perm_str = cell2str(pes{i});
    if isKey(pmf_map, perm_str)
        pmf_vector(i) = pmf_map(perm_str);
    end
end
end

function str = cell2str(cell_array)
% 将cell数组转换为字符串
if isempty(cell_array)
    str = 'empty';
else
    str = strjoin(cell_array, ',');
end
end

function distance = calculate_original_drps_distance(rps1, rps2)
% 计算原论文dRPS距离
rd_matrix = calculate_original_rd_matrix(rps1.pes);
diff_vector = rps1.pmf_vector - rps2.pmf_vector;
distance = sqrt(0.5 * diff_vector' * rd_matrix * diff_vector);
end

function distance = calculate_owa_rbo_distance(rps1, rps2, orness_alpha)
% 计算OWA-RBO距离
% 使用二次型公式: sqrt(0.5 * ΔPMF' * RD_OWA * ΔPMF)

if length(rps1.pes) ~= length(rps2.pes)
    error('RPS must have the same PES');
end

% 计算OWA-RBO的RD矩阵
rd_matrix = calculate_owa_rbo_rd_matrix(rps1.pes, orness_alpha);

% 计算PMF差异向量
diff_vector = rps1.pmf_vector - rps2.pmf_vector;

% 计算距离
distance = sqrt(0.5 * diff_vector' * rd_matrix * diff_vector);
end

function rd_matrix = calculate_owa_rbo_rd_matrix(pes, orness_alpha)
% 计算OWA-RBO的RD矩阵
% 遍历PES中的每一对排列事件(P_i, P_j)并计算它们之间的Sim(P_i, P_j)

n = length(pes);
rd_matrix = zeros(n, n);

for i = 1:n
    for j = 1:n
        P_i = pes{i};
        P_j = pes{j};
        
        % 计算OWA-RBO相似度Sim(P_i, P_j)
        similarity = calculate_owa_rbo_similarity(P_i, P_j, orness_alpha);
        
        % RD矩阵元素就是相似度
        rd_matrix(i, j) = similarity;
    end
end
end

function similarity = calculate_owa_rbo_similarity(A, B, orness_alpha)
% 计算两个排列事件之间的OWA-RBO相似度
% Sim_OWA-RBO(A, B) = Σ_{d=1 to L_long} [w_d * Agreement_d(A, B)]

% 确保A和B是cell数组
if ~iscell(A), A = {A}; end
if ~iscell(B), B = {B}; end

% 处理空排列
if isempty(A) && isempty(B)
    similarity = 1;
    return;
end

if isempty(A) || isempty(B)
    similarity = 0;
    return;
end

% 计算L_long: 两个排列中较长的长度
L_long = max(length(A), length(B));

if L_long == 0
    similarity = 1;
    return;
end

% 生成OWA权重
weights = generate_owa_weights(L_long, orness_alpha);

% 计算每个深度的Agreement_d并加权求和
similarity = 0;
for d = 1:L_long
    agreement_d = calculate_agreement_d(A, B, d);
    similarity = similarity + weights(d) * agreement_d;
end
end

function agreement_d = calculate_agreement_d(A, B, d)
% 计算深度d的一致度
% Agreement_d(A, B) = |A:d ∩ B:d| / d

% 确保A和B是cell数组
if ~iscell(A), A = {A}; end
if ~iscell(B), B = {B}; end

% 获取前d个元素的前缀集合
A_prefix = {};
B_prefix = {};

for i = 1:min(d, length(A))
    A_prefix{end+1} = A{i};
end

for i = 1:min(d, length(B))
    B_prefix{end+1} = B{i};
end

% 计算交集大小
if isempty(A_prefix) && isempty(B_prefix)
    intersection_size = 0;
else
    intersection = intersect(A_prefix, B_prefix);
    intersection_size = length(intersection);
end

% 计算Agreement_d
agreement_d = intersection_size / d;
end

function rd_matrix = calculate_original_rd_matrix(pes)
% 计算原论文RD矩阵
n = length(pes);
rd_matrix = zeros(n, n);

for i = 1:n
    for j = 1:n
        A = pes{i};
        B = pes{j};
        
        % 计算Jaccard系数
        jaccard = calculate_jaccard(A, B);
        
        % 计算原论文有序度
        od = calculate_original_od(A, B);
        
        % RD矩阵元素
        rd_matrix(i, j) = jaccard * od;
    end
end
end

function jaccard = calculate_jaccard(A, B)
% 计算Jaccard系数
if isempty(A) && isempty(B)
    jaccard = 1;
    return;
end
if isempty(A) || isempty(B)
    jaccard = 0;
    return;
end

set_A = unique(A);
set_B = unique(B);
intersection = intersect(set_A, set_B);
union_set = union(set_A, set_B);
jaccard = length(intersection) / length(union_set);
end

function od = calculate_original_od(A, B)
% 计算原论文有序度
if isempty(A) || isempty(B)
    od = 1;
    return;
end

% 确保是cell数组
if ~iscell(A), A = {A}; end
if ~iscell(B), B = {B}; end

% 找到共同元素
common_elements = intersect(A, B);
if isempty(common_elements)
    od = 1;
    return;
end

% 计算位置偏差总和
total_deviation = 0;
for elem = common_elements
    pos_A = find(strcmp(A, elem), 1);
    pos_B = find(strcmp(B, elem), 1);
    if ~isempty(pos_A) && ~isempty(pos_B)
        total_deviation = total_deviation + abs(pos_A - pos_B);
    end
end

% 计算并集大小
union_elements = union(A, B);
union_size = length(union_elements);

if union_size == 0
    od = 1;
else
    od = exp(-total_deviation / union_size);
end
end

function weights = generate_owa_weights(n, orness)
% 基于Orness和最大熵原理生成OWA权重

if n == 1
    weights = 1;
    return;
end

% 验证Orness范围
if orness < 0 || orness > 1
    error('Orness must be in [0, 1]');
end

% 特殊情况处理
if abs(orness - 0.5) < 1e-12
    % 中性权重：均匀分布
    weights = ones(n, 1) / n;
    return;
elseif abs(orness - 1) < 1e-12
    % 完全乐观：第一个位置权重为1
    weights = zeros(n, 1);
    weights(1) = 1;
    return;
elseif abs(orness) < 1e-12
    % 完全悲观：最后一个位置权重为1
    weights = zeros(n, 1);
    weights(n) = 1;
    return;
end

% 使用数值方法求解最优权重
alpha = calculate_alpha_from_orness(orness, n);
weights = zeros(n, 1);

for i = 1:n
    weights(i) = exp(-alpha * (i - 1));
end

% 归一化
weights = weights / sum(weights);

% 微调以满足精确的orness约束
weights = fine_tune_weights(weights, orness, n);
end

function alpha = calculate_alpha_from_orness(orness, n)
% 使用二分法求解alpha参数

if abs(orness - 0.5) < 1e-12
    alpha = 0;
    return;
end

alpha_min = 0;
alpha_max = 10;
tolerance = 1e-6;

for iter = 1:100
    alpha_mid = (alpha_min + alpha_max) / 2;
    
    weights_temp = zeros(n, 1);
    for i = 1:n
        weights_temp(i) = exp(-alpha_mid * (i - 1));
    end
    weights_temp = weights_temp / sum(weights_temp);
    
    orness_temp = 0;
    for i = 1:n
        orness_temp = orness_temp + (n - i) * weights_temp(i);
    end
    orness_temp = orness_temp / (n - 1);
    
    if abs(orness_temp - orness) < tolerance
        alpha = alpha_mid;
        return;
    end
    
    if orness_temp > orness
        alpha_max = alpha_mid;
    else
        alpha_min = alpha_mid;
    end
end

alpha = (alpha_min + alpha_max) / 2;
end

function weights = fine_tune_weights(initial_weights, target_orness, n)
% 微调权重以满足精确的orness约束

weights = initial_weights;
max_iterations = 50;
tolerance = 1e-6;

for iter = 1:max_iterations
    current_orness = 0;
    for i = 1:n
        current_orness = current_orness + (n - i) * weights(i);
    end
    current_orness = current_orness / (n - 1);
    
    error = current_orness - target_orness;
    
    if abs(error) < tolerance
        break;
    end
    
    % 简单的梯度调整
    adjustment = error * 0.1;
    for i = 1:n
        weights(i) = weights(i) - adjustment * (n - i) / (n - 1);
    end
    
    % 确保权重非负且归一化
    weights = max(weights, 1e-15);
    weights = weights / sum(weights);
end
end

function display_rps(rps, name)
% 显示RPS信息
fprintf('\n%s:\n', name);
for i = 1:length(rps.pmf_vector)
    if rps.pmf_vector(i) > 0
        perm_str = cell2str(rps.pes{i});
        fprintf('  <%s>: %.3f\n', perm_str, rps.pmf_vector(i));
    end
end
end