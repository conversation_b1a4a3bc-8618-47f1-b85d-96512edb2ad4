%% TW-dRPS Core Implementation - 核心步骤实现
% 基于"总不一致度"模型的RPS距离计算核心流程
%
% 核心步骤: RPS输入 → TW-dRPS计算 → SIM矩阵 → Sup → Cre

clear; clc;
fprintf('=== TW-dRPS Core Implementation ===\n');
fprintf('核心步骤: RPS输入 → TW-dRPS计算 → SIM矩阵 → Sup → Cre\n\n');

%% 步骤1: RPS输入
fprintf('步骤1: RPS输入\n');
fprintf('==============\n');

% 定义元素集合
elements = {'A', 'B', 'C'};
fprintf('元素集合: %s\n', strjoin(elements, ', '));

% 创建3个RPS
rps1_pmf = containers.Map();
rps1_pmf('A,B,C') = 0.6;
rps1_pmf('A,C,B') = 0.4;

rps2_pmf = containers.Map();
rps2_pmf('B,A,C') = 0.7;
rps2_pmf('B,C,A') = 0.3;

rps3_pmf = containers.Map();
rps3_pmf('C,A,B') = 0.5;
rps3_pmf('C,B,A') = 0.5;

% 创建RPS结构
rps1 = create_rps(elements, rps1_pmf);
rps2 = create_rps(elements, rps2_pmf);
rps3 = create_rps(elements, rps3_pmf);

% 显示RPS
display_rps(rps1, 'RPS1');
display_rps(rps2, 'RPS2');
display_rps(rps3, 'RPS3');

%% 步骤2: dRPS距离计算对比
fprintf('\n步骤2: dRPS距离计算对比\n');
fprintf('=======================\n');

rps_list = {rps1, rps2, rps3};
n_rps = length(rps_list);
orness = 0.8;  % 顶部偏置参数

% 计算原论文dRPS距离矩阵
fprintf('原论文dRPS距离矩阵:\n');
distance_matrix_original = zeros(n_rps, n_rps);
for i = 1:n_rps
    for j = 1:n_rps
        if i ~= j
            distance_matrix_original(i,j) = calculate_original_drps_distance(rps_list{i}, rps_list{j});
        end
    end
end
disp(distance_matrix_original);

% 计算改进的TW-dRPS距离矩阵
fprintf('\nTW-dRPS距离矩阵 (Orness = %.1f):\n', orness);
distance_matrix = zeros(n_rps, n_rps);
for i = 1:n_rps
    for j = 1:n_rps
        if i ~= j
            distance_matrix(i,j) = calculate_tw_drps_distance(rps_list{i}, rps_list{j}, orness);
        end
    end
end
disp(distance_matrix);

% 计算差异矩阵
fprintf('\n距离差异矩阵 (TW-dRPS - 原dRPS):\n');
difference_matrix = distance_matrix - distance_matrix_original;
disp(difference_matrix);

%% 步骤3: 相似度矩阵(SIM)
fprintf('\n步骤3: 相似度矩阵(SIM)\n');
fprintf('=====================\n');

% 计算相似度矩阵: SIM = 1 - Distance
sim_matrix = 1 - distance_matrix;
for i = 1:n_rps
    sim_matrix(i,i) = 1;  % 对角线元素为1
end

fprintf('相似度矩阵:\n');
disp(sim_matrix);

%% 步骤4: 支持度(Sup)
fprintf('\n步骤4: 支持度(Sup)\n');
fprintf('==================\n');

% 计算支持度: Sup(RPSi) = Σ SIM(i,j) - 1 (j≠i)
support = zeros(n_rps, 1);
for i = 1:n_rps
    support(i) = sum(sim_matrix(i, :)) - 1;
end

fprintf('支持度:\n');
for i = 1:n_rps
    fprintf('Sup(RPS%d) = %.6f\n', i, support(i));
end

%% 步骤5: 可信度(Cre)
fprintf('\n步骤5: 可信度(Cre)\n');
fprintf('==================\n');

% 计算可信度: Cre(RPSi) = Sup(RPSi) / Σ Sup(RPSj)
total_support = sum(support);
credibility = support / total_support;

fprintf('可信度:\n');
for i = 1:n_rps
    fprintf('Cre(RPS%d) = %.6f\n', i, credibility(i));
end

fprintf('\n可信度验证: Σ Cre = %.6f (应该等于1)\n', sum(credibility));

fprintf('\n=== TW-dRPS核心流程完成 ===\n');

%% ========================================================================
%% 核心函数实现
%% ========================================================================

function rps = create_rps(elements, pmf_map)
% 创建RPS结构
rps.elements = elements;
rps.pes = generate_pes(elements);
rps.pmf_map = pmf_map;
rps.pmf_vector = create_pmf_vector(rps.pes, pmf_map);
end

function pes = generate_pes(elements)
% 生成排列事件空间
n = length(elements);
pes = {};

% 添加空集
pes{end+1} = {};

% 生成所有排列
for i = 1:n
    combinations = nchoosek(1:n, i);
    for c = 1:size(combinations, 1)
        selected_elements = elements(combinations(c, :));
        if i == 1
            pes{end+1} = selected_elements;
        else
            perm_indices = perms(1:i);
            for p = 1:size(perm_indices, 1)
                pes{end+1} = selected_elements(perm_indices(p, :));
            end
        end
    end
end
end

function pmf_vector = create_pmf_vector(pes, pmf_map)
% 创建PMF向量
pmf_vector = zeros(length(pes), 1);
for i = 1:length(pes)
    perm_str = cell2str(pes{i});
    if isKey(pmf_map, perm_str)
        pmf_vector(i) = pmf_map(perm_str);
    end
end
end

function str = cell2str(cell_array)
% 将cell数组转换为字符串
if isempty(cell_array)
    str = 'empty';
else
    str = strjoin(cell_array, ',');
end
end

function distance = calculate_original_drps_distance(rps1, rps2)
% 计算原论文dRPS距离
rd_matrix = calculate_original_rd_matrix(rps1.pes);
diff_vector = rps1.pmf_vector - rps2.pmf_vector;
distance = sqrt(0.5 * diff_vector' * rd_matrix * diff_vector);
end

function distance = calculate_tw_drps_distance(rps1, rps2, orness)
% 计算TW-dRPS距离
rd_matrix = calculate_rd_matrix(rps1.pes, orness);
diff_vector = rps1.pmf_vector - rps2.pmf_vector;
distance = sqrt(0.5 * diff_vector' * rd_matrix * diff_vector);
end

function rd_matrix = calculate_original_rd_matrix(pes)
% 计算原论文RD矩阵
n = length(pes);
rd_matrix = zeros(n, n);

for i = 1:n
    for j = 1:n
        A = pes{i};
        B = pes{j};
        
        % 计算Jaccard系数
        jaccard = calculate_jaccard(A, B);
        
        % 计算原论文有序度
        od = calculate_original_od(A, B);
        
        % RD矩阵元素
        rd_matrix(i, j) = jaccard * od;
    end
end
end

function rd_matrix = calculate_rd_matrix(pes, orness)
% 计算TW-dRPS RD矩阵
n = length(pes);
rd_matrix = zeros(n, n);

for i = 1:n
    for j = 1:n
        A = pes{i};
        B = pes{j};
        
        % 计算Jaccard系数
        jaccard = calculate_jaccard(A, B);
        
        % 计算改进的有序度
        od = calculate_tw_od(A, B, orness);
        
        % RD矩阵元素
        rd_matrix(i, j) = jaccard * od;
    end
end
end

function jaccard = calculate_jaccard(A, B)
% 计算Jaccard系数
if isempty(A) && isempty(B)
    jaccard = 1;
    return;
end
if isempty(A) || isempty(B)
    jaccard = 0;
    return;
end

set_A = unique(A);
set_B = unique(B);
intersection = intersect(set_A, set_B);
union_set = union(set_A, set_B);
jaccard = length(intersection) / length(union_set);
end

function od = calculate_original_od(A, B)
% 计算原论文有序度 OD(A,B) = exp(-Σ|rank_A(Y) - rank_B(Y)| / |A∪B|)

if isempty(A) || isempty(B)
    od = 1;
    return;
end

% 确保是cell数组
if ~iscell(A), A = {A}; end
if ~iscell(B), B = {B}; end

% 找到共同元素
common_elements = intersect(A, B);
if isempty(common_elements)
    od = 1;
    return;
end

% 计算位置偏差总和
total_deviation = 0;
for elem = common_elements
    pos_A = find(strcmp(A, elem), 1);
    pos_B = find(strcmp(B, elem), 1);
    if ~isempty(pos_A) && ~isempty(pos_B)
        total_deviation = total_deviation + abs(pos_A - pos_B);
    end
end

% 计算并集大小
union_elements = union(A, B);
union_size = length(union_elements);

if union_size == 0
    od = 1;
else
    od = exp(-total_deviation / union_size);
end
end

function od = calculate_tw_od(A, B, orness)
% 计算TW-dRPS改进的有序度
% 实现: OD_weighted = 1 - Total_Disagreement

if isempty(A) && isempty(B)
    od = 1;
    return;
end

% 确保是cell数组
if ~iscell(A), A = {A}; end
if ~iscell(B), B = {B}; end

% L_long: 较长排列的长度
L_long = max(length(A), length(B));

if L_long == 0
    od = 1;
    return;
end

% 生成位置权重
weights = generate_owa_weights(L_long, orness);

% 计算总不一致度
total_disagreement = 0;
for d = 1:L_long
    disagreement_d = calculate_disagreement(A, B, d);
    total_disagreement = total_disagreement + weights(d) * disagreement_d;
end

% OD_weighted = 1 - Total_Disagreement
od = 1 - total_disagreement;
od = max(0, min(1, od));  % 确保在[0,1]范围内
end

function disagreement = calculate_disagreement(A, B, position)
% 计算位置position的不一致度
% Disagreement_d = 0 如果相同, 1 如果不同或长度惩罚

has_A = position <= length(A);
has_B = position <= length(B);

if ~has_A && ~has_B
    disagreement = 0;  % 都没有该位置
elseif ~has_A || ~has_B
    disagreement = 1;  % 长度惩罚
else
    % 比较元素是否相同
    if strcmp(A{position}, B{position})
        disagreement = 0;
    else
        disagreement = 1;
    end
end
end

function weights = generate_owa_weights(n, orness)
% 基于Orness生成OWA权重

if n == 1
    weights = 1;
    return;
end

if abs(orness - 0.5) < 1e-12
    % 均匀权重
    weights = ones(n, 1) / n;
    return;
end

% 使用指数衰减生成权重
alpha = calculate_alpha(orness, n);
weights = zeros(n, 1);
for i = 1:n
    weights(i) = exp(-alpha * (i - 1));
end
weights = weights / sum(weights);
end

function alpha = calculate_alpha(orness, n)
% 计算alpha参数
alpha_min = 0;
alpha_max = 10;
tolerance = 1e-6;

for iter = 1:100
    alpha_mid = (alpha_min + alpha_max) / 2;
    
    weights_temp = zeros(n, 1);
    for i = 1:n
        weights_temp(i) = exp(-alpha_mid * (i - 1));
    end
    weights_temp = weights_temp / sum(weights_temp);
    
    orness_temp = 0;
    for i = 1:n
        orness_temp = orness_temp + (n - i) * weights_temp(i);
    end
    orness_temp = orness_temp / (n - 1);
    
    if abs(orness_temp - orness) < tolerance
        alpha = alpha_mid;
        return;
    end
    
    if orness_temp > orness
        alpha_max = alpha_mid;
    else
        alpha_min = alpha_mid;
    end
end

alpha = (alpha_min + alpha_max) / 2;
end

function display_rps(rps, name)
% 显示RPS信息
fprintf('\n%s:\n', name);
for i = 1:length(rps.pmf_vector)
    if rps.pmf_vector(i) > 0
        perm_str = cell2str(rps.pes{i});
        fprintf('  <%s>: %.3f\n', perm_str, rps.pmf_vector(i));
    end
end
end