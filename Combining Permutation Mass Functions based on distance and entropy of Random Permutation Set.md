<!-- Information Sciences 692 (2025)121657 -->

<!-- Contents lists available at ScienceDirect -->

<!--  -->

<!-- INFORMATION SCIENCES -->

<!-- **Information Sciences** -->

<!-- ELSEVIER journal homepage:www.elsevier.com/locate/ins -->

<!-- Check for updates -->
![](https://web-api.textin.com/ocr_image/external/9e4adfe004d8f664.jpg)

# Combining Permutation Mass Functions based on distance and entropy of Random Permutation Set

**Linshan <PERSON>, Puhantong Rongb, Meizhu LiC,***

aSchool of Electrical and Information Engineering, Jiangsu University,Zhenjiang 212013,China

bSchool of Mathematics and Physics,Xi'an Jiaotong-Liverpool University,Suzhou,215028,China

School of Computer Science and Communication Engineering, Jiangsu University, Zhenjiang 212013,China

## ARTICLE INFO

Keywords:

Evidence theory

Random Permutation Set

Distance of Random Permutation Set theory

Permutation mass function

Information fusion

## ABSTRACT

A novel set type, termed Random Permutation Set (RPS), has recently been introduced to account for permutations of sets. Serving as an extension of evidence theory, the concern about whether it might yield counterintuitive outcomes when confronted with high conflict akin to evidence theory arises as a pertinent issue in practical engineering applications. In this paper, we initially explore the outcomes of direct fusion amidst varying levels of extreme conflict. Following this,we innovatively proposed a fusion method based on RPS distance and entropy metrics. This method utilizes the distances between RPS for weighting and determines the final RPS subset used for weighting through the entropy of the RPS. Through the presentation of several examples and specific experiments, we demonstrate its efficacy in handling extreme conflict scenarios and enhancing the quality of fusion outcomes.

## 1. Introduction

Given the omnipresence of uncertainty and imprecision within various real-world practical projects [1], coupled with the inherent challenge of completely eliminating such phenomena, an increasing number of scholars are directing their focus towards formulat-ing uncertain and imprecise models. Within this domain, numerous theoretical frameworks [2,3] have been advanced to address uncertainty and have been applied to many practical problems [4,5]. In particular, evidence theory [6,7] has garnered widespread adoption owing to its applicability within scenarios characterized by constrained prior information [8], alongside its commendable attributes, such as adherence to the commutativity law [9]. In practical applications, such as information fusion [10,11],pattern recognition [12,13], risk assessment [14,15] and so on [16,17], evidence theory has become an indispensable means to deal with imprecise information.

In an advancement beyond evidence theory, Deng [18] recently introduced the Random Permutation Set (RPS). His proposal not only delves into the interpretation of the power set through the lens of the Pascal triangle and combination numbers but also advocates for substituting combination numbers with permutation numbers, resulting in the emergence of RPS as a sophisticated conceptual framework. The Random Permutation Set comprises a permutation event space (PES) and a Permuutation Mass Function (PMF). The PES encompasses all permutations of the elements within the set, while the PMF quantifies the chance associated with each permutation event. The larger the value of the Permutation Mass Function of the permutation event is, the greater the chance of the occurrence, and the sum of the Permutation Mass Function of all the permutation events is 1. In addition to this, Deng also

* Corresponding author.

E-mail address: <EMAIL> (M.Li).

https://doi.org/10.1016/j.ins.2024.121657

Received 24 March 2024; Received in revised form 8 November 2024; Accepted 13 November 2024

Available online 17 November 2024

0020-0255/© 2024 Elsevier Inc. All rights are reserved, including those for text and data mining, AI training, and similar technologies.

<!-- L.Li,P.Rong and M.Li Information Sciences 692(2025)121657 -->

proposed the left orthogonal sum (LOS) and the right orthogonal sum (ROS) to fuse multiple RPSs. It has been proved that RPS has great potential value in many practical applications [19,20], and the research on the theoretical framework of RPS has vital practical significance. Compared with evidence theory, RPST not only retains many excellent properties to facilitate fusion and decision-making, but also can use the arrangement between events to carry additional information [21,22], which makes it more flexible in dealing with uncertain information.

As RPS can be seen as a generalization of evidence theory, its left orthogonal sum and right orthogonal sum bear resemblance to the orthogonal combination approach within evidence theory. Nevertheless, it prompts contemplation on whether these methodologies might engender counterintuitive outcomes when faced with highly conflicting information [23,24], akin to the challenges inherent in evidence theory. Scholarly discourse within evidence theory has extensively explored strategies for managing highly conflicting sce-narios, typically falling into two main approaches: adjusting fusion rules [25,26] or preprocessing fused data [27,28].However, it is paramount to acknowledge that modifying fusion rules often necessitates compromises to desirable properties such as commutativity and associativity. Furthermore, such modifications might prove ineffectual in rectifying counterintuitive outcomes stemming from sensor failures. Hence, any endeavors to reinforce specific aspects of evidence theory through rule adjustments demand a delicate equilibrium, as unintended consequences may manifest, particularly concerning alterations to fundamental properties and address-ing sensor failure scenarios. Alternatively, preprocessing information has emerged as a prevalent trend [27]. Initially proposed by Murphy [29],averaging the basic probability assignments (BPA) is a method capable of managing conflicting situations but at the expense of losing significant information inherent in the original BPA. Subsequently, Deng et al. [30] introduced a weighted fusion technique based on evidence distance, adept at addressing extreme conflict scenarios while retaining more inherent information from the original BPA compared to Murphy's approach. This method has found widespread adoption owing to its ability to effectively handle conflicting evidence and extract additional pertinent information. It can be seen that the distance between evidences also plays an important role in dealing with highly conflicting information [31].

The concept of entropy [32] originally comes from physics to represent how chaotic a system is. In probability theory,Shannon [33]proposed Shannon entropy for quantifying uncertainty. Similarly, in evidence theory, many scholars have tried to use entropy to quantify the uncertainty of evidence [34]. Among them, Deng entropy [35] is a representative one, which has been proven to have good properties [36] and has been applied to practical problems [37,38]. With the proposal of RPS, Deng further improved his framework and proposed the entropy of RPS [39], which was used to quantify the information quality of RPS. Building on these predecessors, we now have sophisticated methods for quantifying information quality. In reference [40], a method of multi-source fusion based on information quality was mentioned, so we were inspired to use the entropy of RPS to quantify the information quality and further search for a method of fusion PMF.

In this paper, weaim to address the limitations of traditional RPS fusion methods, specifically LOS and ROS, particularly in conflict scenarios. Through a series of representative examples, we demonstrate that the direct application of LOS and ROS inherits the shortcomings of evidence theory's orthogonal fusion, leading to counterintuitive results. To overcome these challenges, we propose a novel fusion approach. Initially, we calculate the credibility degree coefficient by analyzing the distances between RPS [41],which allows us to derive a weighted subset of RPS. We then assess the entropy of these weighted subsets to identify the one with the highest information quality. Ultimately, we utilize this high-quality RPS for fusion, employing either LOS or ROS to generate the final results.Our experiments, including applications in risk assessment, illustrate that our method effectively addresses RPS fusion in extreme conflict cases and significantly enhances the quality of the final fusion results. This innovative approach underscores the motivation behind our research, aiming to improve the robustness and reliability of evidence-based decision-making processes.

The manuscript is structured as follows in the subsequent sections: Section 2 provides a comprehensive review of the basic concepts of RPS and clarifies the distance and entropy of RPS. In Section 3, we enumerate three different extreme conflict cases to illustrate the drawbacks of LOS and ROS in the face height case with examples, and Section 4 elaborates on our newly proposed fusion method for RPS. In addition, Section 5 provides four illustrative examples to further clarify the superiority of our approach in dealing with conflict situations and improving the information quality of fusion results. In Section 6, we apply the proposed method to a risk assessment experiment and further demonstrate the effectiveness of our method by analyzing the experimental results. Section 7 generalizes a summary of the advantages and inherent limitations of the proposed approach, ultimately describing prospective avenues for future research exploration.

## 2. Preliminaries

This section focuses on the basic preparatory knowledge for the understanding of the paper, mainly the introduction of some concepts and definitions of Random Permutation Set.

### 2.1. Random Permutation Set

The Random Permutation Set (RPS) [18] represents a novel set concept that encompasses permutations of elements, serving as an extension of evidence theory. In this subsection, we will give some definitions related to RPS to facilitate the understanding of this paper.

Definition 2.1 (Permutation Event Space). A pre-established set comprising N elements $\mathbb {Y}=\left\{Y_{1},Y_{2},\cdots ,Y_{N}\right\}$ ,where the elements are mutually exclusive and collectively exhaustive, is considered. The definition of the set's permutation event space (PES) is outlined as follows:

<!-- 2 -->

<!-- L.Li,P.Rong and M.Li Information Sciences 692 (2025) 121657 -->

$$PES(\mathbb {Y})\ !=\ !\{A_{ij}\vert i\ !=\ !0,...,N;j=1,...,P(N,i)\}$$

$$=\left\{\emptyset ,\left(Y_{1}\right),\left(Y_{2}\right),\ldots ,\left(Y_{N}\right),\left(Y_{1},Y_{2}\right),\left(Y_{2},Y_{1}\right),\ldots ,\left(Y_{N-1},Y_{N}\right)\right.\tag{1}$$

$$\left.\left(Y_{N},Y_{N-1}\right),\ldots ,\left(Y_{1},Y_{2},\ldots ,Y_{N}\right),\ldots ,\left(Y_{N},Y_{N-1},\ldots ,Y_{1}\right)\right\}$$

In this context, $P(N,i)$  represents the i-th permutation ofN,with the condition that $P(N,i)=\frac {N!}{(N-i)!}$ . The permutation **event** denoted by the element $A_{ij}$  in PES refers to a tuple that represents a potential permutation of $人$ . Here, the index $i$  signifies the cardinality of $A_{ij},$  while the index $j$  indicates the possible permutation within the set.

Definition 2.2 (Random Permutation Set). In the presence of a defined set of N mutually exclusive and exhaustive elements, denoted as $\mathbb {Y}=\left\{Y_{1},Y_{2},\cdots ,Y_{N}\right\}$ , the Random Permutation Set (RPS) manifests as a grouping of pairs:

$$RPS(\mathbb {Y})=\{\langle A,\mathcal {M}(A)\rangle \vert A\in PES(\mathbb {Y})\}\tag{2}$$

In this context, $M$  is referred to as the permutation Mass Function (PMF), which is elucidated as:

$$\mathcal {M}:\text {PES}(\mathbb {Y})\rightarrow [0,1].\tag{3}$$

And it satisfies the following two conditions:

$$\mathcal {M}(\emptyset )=0,\sum _{A\in PES(\mathbb {Y})}\mathcal {M}(A)=1.\tag{4}$$

If the arrangement of elements in the Permutation Event Set is disregarded, the Permutation Mass Function of the RPS will degrade to Basic Probability Assignment (BPA) in evidence theory.

**Definition** 2.3 (Intersection of permutation events). The Right Intersection (RI) and Left Intersection (LI) of events A and B,both belonging to the Permutation Event Set (PES) of set $A$ , are defined as follows:

$\stackrel {\leftarrow }{A\cap B}=A\backslash \backslash bigcap_{Y\in A,Y\notin B}\{Y\}$  (LI). (5)

$\overrightarrow {A\cap B}=B\backslash \backslash bigcap_{Y\in B,Y\notin A}\{Y\}$ (RI). (6)

Here, $X_{1}\backslash \backslash X_{2}$ signifies the removal of $X_{2}$ form $X_{1}$  through the permutation of elements in $X_{1}$ . Upon closer examination of the formula,it becomes apparent that $\vert A\cap B\vert$ is equivalent to $\vert \stackrel {\leftarrow }{\wedge }B\vert .$ 

**Definition** **2.4** (Left orthogonal sum of Permutation Mass Functions). For two RPSs defined on $Y,$ $\mathcal {M}_{1}$ and $\mathcal {M}_{2},$ , the left orthogonal sum is denoted as $\mathcal {M}_{1}\oplus \mathcal {M}_{2}$ and its mathematical expression is as follows:

$$\mathcal {M}^{L}(A)=\left\{\begin{array}{ll}\frac {1}{1-\stackrel {\leftarrow }{\mathrm {\sim K}}}\sum _{\stackrel {\leftarrow }{B\cap C=A}}\mathcal {M}_{1}(B)\mathcal {M}_{2}(C),\\ 0,&A=\emptyset \end{array}\right.\quad \{\}_{x}^{x}\tag{7}$$

In this context,A, $B$ $,C\in PES(\mathbb {Y}),$  the representative of $\stackrel {\leftarrow }{\cap }$ is denoted as the left intersection. In addition, the mathematical expression $K$  represents is,

$$\stackrel {\leftarrow }{\mathrm {K}}=\sum _{\substack {\leftarrow \\ B\cap C=\emptyset }}\mathcal {M}_{1}(B)\mathcal {M}_{2}(C).\tag{8}$$

**Definition** 2.5 (Right orthogonal sum of Permutation Mass Functions). For two RPSs defined on $4$ ,m1 and m2, the right orthogonal sum is denoted as $\mathcal {M}_{1}\overrightarrow {\oplus }\mathcal {M}_{2}$ and its mathematical expression is as follows:

$$\mathcal {M}^{R}(A)=\left\{\begin{array}{l}\frac {1}{1-\mathrm {K}}\sum _{\overrightarrow {B\cap C=A}}\mathcal {M}_{1}(B)\mathcal {M}_{2}(C),\\ 0,\quad A=\varnothing \end{array}\right.\quad A\neq \emptyset\tag{9}$$

In this context, A,B, $C\in PES(\mathbb {Y})$ ,the representative of $\overrightarrow {\cap }$ iis denoted as the right intersection. In addition, the mathematical expression $\overrightarrow {\mathrm {K}}$ represents is,

<!-- 3 -->

<!-- L.Li,P.Rong and M.Li Information Sciences 692 (2025) 121657 -->

<!-- $$\overrightarrow {\mathrm {K}}=\sum _{\substack {\rightarrow \\ B\cap C=\emptyset }}\mathcal {M}_{1}(B)\mathcal {M}_{2}(C).\tag{10}$$ -->

### 2.2.The distance of Random Permutation Set

In this subsection, we will introduce an idea based on the weighted vector proposed by Jousselme et al. [42], for the distance measure between BPAs, and improve and apply the distance measure between RPSs [41].

**Definition** **2.6** (Union of permutation events). The Left Union and Right Union for two specified permutation events $A$  and B are outlined as follows:

$\overrightarrow {A\cup B}=B\parallel \bigcup _{Y\in A,Y\notin B}\{Y\}$  (RU). (11) $\stackrel {\leftarrow }{A\cup }B=A\parallel \bigcup \quad \{Y\}$  (LU). (12) $Y\in B,Y\notin A$ 

The expression $X_{1}\parallel X_{2}$ signifies the addition of $X_{2}$ to the end of $X_{1}$ while maintaining the permutation of elements within $X_{2}$ .Upon closer examination of the formula, it becomes apparent that $\vert \overrightarrow {A\cup }B\vert$ is equivalent to $\vert A\overleftarrow {\cup }B\vert .$ 

**Definition** **2.7** (Ordered degree of permutation events). When pondering over two permutation events $A$ and B within $PES(\mathbb {Y})$ , the depiction of their ordered degree is as follows:

$$OD(A,B)=\exp \left(-\frac {\sum _{Y\in A\cap B}\left|\operatorname {rank}_{A}(Y)-\operatorname {rank}_{B}(Y)\right|}{|A\cup B|}\right)\tag{13}$$

Here,rank A and rankB represent the order of $Y$  in permutation events A and B. The term of the exponent in the formula above, $-\frac {\sum _{Y\in A\cap B}\left|\operatorname {rank}_{A}(Y)-\operatorname {rank}_{B}(Y)\right|}{|A\cup B|}$ , is defined as the pseudo-deviation distance. When the permutation events are exactly the same, $OD(A,B)=1$ . Whnth permutation events have more elements with different orders, their order degree is smaller, that is, $OD(A,B)$ is also smaller.

**Definition** 2.8 (Distance of Random Permutation Set). For two defined RPSs, $RPS_{1}$ and RP $S_{2}$ ,which have the same set of permutation events Y, we define their distance using the following formula:

$$d_{RPS}\left(\overrightarrow {RPS_{1}},\overrightarrow {RPS_{2}}\right)=\sqrt {\frac {1}{2}\left(\overrightarrow {RPS_{1}}-\overrightarrow {RPS_{2}}\right)\underline {RD}\left(\overrightarrow {RPS_{1}}-\overrightarrow {RPS_{2}}\right)^{T}}\tag{14}$$

In this given context, $\overrightarrow {RPS_{1}}$ and $\overrightarrow {RPS_{2}}$ are represented as vectors whose coordinates are $M(A)$  . $RD$  is a $K*K$ symmetric matrix,and the value of each element in the matrix is given by the following formula:

$$\underline {RD}(A,B)=\frac {\vert A\vec {\cap }B\vert }{\vert \overrightarrow {A\cup }B\vert }\times OD(A,B)\tag{15}$$

It is evident from the existing literature that the distance metric of RPS adheres to favorable properties, including exchange rate and non-negativity.

### 2.3. The entropy of RPS

**Definition** 2.9 (Entropy of RPS). For a Random PermutationSet represented as $RPS(\mathbb {Y})=\left\{<A_{ij},\right.$ ,M(A1j)&gt;|A1jEPi $ES(\mathbb {Y})\vert \}$ ,the entropy [43] of the Random Permutation Set is expressed as:

$$H_{\mathrm {RPS}}(\mathcal {M})=-\sum _{i=1}^{N}\sum _{j=1}^{P(N,i)}\mathcal {M}\left(A_{ij}\right)\log \left(\frac {\mathcal {M}\left(A_{ij}\right)}{F(i)-1}\right)\tag{16}$$

Where F (i) represents the sum of all permutations of elements that do not satisfy the reliability criterion. The mathematical formula is as follows:

$$F\left(i\right)=\sum _{k=0}^{i}P\left(i,k\right)=\sum _{k=0}^{i}\frac {i!}{\left(i-k\right)!}.\tag{17}$$

The entropy of RPS can be used to describe the information quality of RPS. When the entropy value is smaller, the chaos degree is lower and the information quality is higher. The higher the entropy value, the higher the chaos and the lower the quality of the information, that is, the less information fordecision-making.

<!-- L.Li,P.Rong and M.Li Information Sciences 692(2025)121657 -->

Table **1**

Each value obtained for LOS and ROS in Ex-ample 3.1.


|  | $(A)$ | $(B,C)$ | $(C,B)$ | $(D)$ |
| --- | --- | --- | --- | --- |
| $\mathcal {M}^{L}$ | 0  | 0.5  | 0.5  | 0  |
| $\mathcal {M}^{R}$ | 0  | 0.5  | 0.5  | 0  |


## 3. Discussion of serious conflict situations

Given that RPS is an extension of D-S evidence theory, addressing potential challenges becomes essential. When there is significant conflict between evidence sources, applying classical fusion rules from evidence theory may lead to unintuitive results. In this section,we will explore the outcomes that arise from the direct application of the left orthogonal sum and right orthogonal sum when encountering pronounced conflicts between two RPSs.

**Example** 3.1. For a given finite set of mutually exclusive elements, $\mathbb {Y}=\{A,B,C,D\}$ , there are two Random Permutation Sets defined on it, which are expressed as follows:

$$RPS_{1}=\{<(A),0.98>,<(B,C),0.01>,<(C,B),0.01>,<(D),0>\},$$

$$RPS_{2}=\{<(A),0>,<(B,C),0.01>,<(C,B),0.01>,<(D),0.98>\}.$$

In the following, we will give the detailed steps of $Deng's$ s proposal [18] of LOS and ROS.

$\rightarrow$  $\stackrel {\leftarrow }{\mathrm {K}}$ **Step.1.** Using Equations (8) and (10), calculate K and as follows:

$$\overrightarrow {\mathrm {K}}=\sum _{\overrightarrow {\mathrm {K}}\cap C-\mathrm {d}}\mathcal {M}_{1}(B)\mathcal {M}_{2}(C),\quad B\vec {\cap }C=\emptyset$$

$$=0.98x0.01+0.98x0.01+0.98x0.98+0.01x0.98+0.01x0.98,$$

$$=0.9996.$$

$$\overleftarrow {\mathrm {K}}=\sum _{\leftarrow }\mathcal {M}_{1}(B)\mathcal {M}_{2}(C),\quad B\cap C=\emptyset$$

$$=0.98x0.01+0.98x0.01+0.98x0.98+0.01x0.98+0.01x0.98,$$

$$=0.9996.$$

**Step.2.** Using Equations (7) and (9), calculate $\mathcal {M}^{R}$ and $\mathcal {M}^{L}$ as follows:

$$\mathcal {M}^{L}(A)=\mathcal {M}_{1}\stackrel {\leftarrow }{\oplus }\mathcal {M}_{2}=\frac {1}{1-\stackrel {\leftarrow }{\mathrm {K}}}\sum _{B\cap C=A}\mathcal {M}_{1}(B)\mathcal {M}_{2}(C)=0\quad \mathcal {M}^{R}(A)=\mathcal {M}_{1}\overrightarrow {\oplus }\mathcal {M}_{2}=\frac {1}{1-\overrightarrow {\mathrm {K}}}\sum _{\substack {\leftarrow \\ B\cap C=A}}\mathcal {M}_{1}(B)\mathcal {M}_{2}(C)=0.$$

Applying the same methodology, we depict the computed values in Table 1.

From the results obtained, it is not difficult to find that RPS and evidence theory have similar limitations, that is, in the face of highly conflicting situations, LOS and ROS both appear to obtain counterintuitive results.

In Example 3.1, we assigned the highest weight to ((A)in RF $S_{1}$ , the highest weight to $(D)$  in RP $S_{2}$ ,respectively,and very small weights to $(B,C)$ and $(C,B)$  in both two RPSs. However, after ROS and LOS, the corresponding values of $(A)$  and ( $D$  are both 0,which obviously goes against our intuition. This also happens in evidence theory and is known as Zadeh's paradox [44].

**Example** 3.2. For a given finite set of mutually exclusive elements, $\mathbb {Y}=\{A,B,C\}$ ,there are two Random Permutation Sets defined on it, which are expressed as follows:

$$RPS_{1}=\{<(A),0.99>,<(B,C),0.01>,<(C,B),0>\},\quad RPS_{2}=\{<(A),0>,<(B,C),0.01>,<(C,B),0.99>\}$$

In Example 3.2, we employ the Left Orthogonal Sum (LOS) and Right Orthogonal Sum (ROS) to compute $\mathcal {M}^{L}$ and $\mathcal {M}^{R}$ .The calculation steps mirror those outlined in Example 3.1, and the results are directly presented in Table 2 below.

For instance 3.2, we observe that the outcome produced by LOS deviates significantly from our intuition, as bothRP $_{1}$ and RH $S_{2}$ assign the minimum weight to $(B,C)$ ; however, the fusion result reaches the maximum value of 1. The result generated by ROS also

<!-- 5 -->

<!-- L.Li,P.Rong and M.Li Information Sciences 692 (2025) 121657 -->

Table **2**

Each value obtained for LOS and ROS in Example 3.2.


|  | (A)  | $(B,C)$ | $(C,B)$ |
| --- | --- | --- | --- |
| $\mathcal {M}^{L}$ | 0  | 1  | 0  |
| $\mathcal {M}^{R}$ | 0  | 0.01  | 0.99  |


**Table** 3

Each value obtained for LOS and ROS in Exam-ple 3.3.


|  | (A,B,C)  | (A,C,B)  | $(B,A,C)$ |
| --- | --- | --- | --- |
| $\mathcal {M}^{L}$ | 0.99  | 0.01  | 0  |
| $\mathcal {M}^{R}$ | 0  | 0.01  | 0.99  |


appears counterintuitive, as it seems to entirely obscure the information contributed by $RPS_{1}$ . This outcome, however, somewhat aligns with the literature's discussion on LOS and ROS choices-namely, that placing greater trust in a particular information source can cause the orthogonal summation to favor that source. Nevertheless, the complete suppression of one side's information is unde-sirable, as the goal of fusion is to synthesize muItiple information sources effectively. Therefore, the results yielded by ROS and LOS do not meet our expectations for handling highly conflicting situations like Example 3.2.

**Example** 3.3. For a given finite set of mutually exclusive elements, $\mathbb {Y}=\{A,B,C\}$ , there are two Random Permutation Sets defined on it, which are expressed as follows:

$$RPS_{1}=\{<(A,B,C),0.99>,<(A,C,B),0.01>,<(B,A,C),0>\},\quad RPS_{2}=\{<(A,B,C),0>,<(A,C,B),0.01>,<(B.A,C),0.99>\}.$$

In Example 3.3, the third special conflict case is selected, wherein the conflict within the Random Permutation Set arises from the permutation of events and remains independent of event combination. Despite this, the Left orthogonal sum and Right orthogonal sum are still utilized for fusion, and the detailed results are presented in Table 3.

Through the analysis of the results of the table, we can get that when this extreme conflict occurs only because the events are arranged differently, but the combination is the same, it may cause the information of one party to be completely obscured. When LOS is used, the fused result is exactly the same as $RPS_{1}$ on the left, while the information of RF $S_{2}$ is masked out. With ROS, the information on the left is completely covered. This is obviously very different from our original intention of fusing the two information sources.

## 4. Combining Permutation Mass Functions based on distance and entropy of Random Permutation Set

We have found that RPS through the previous section, as an extension of evidence theory, the fusion results tend to obtain counterintuitive results facing the same problem as evidence theory when the evidence is highly conflicting. The predecessors have proposed many methods under the framework of evidence theory, which can be mainly classified into two categories, one is the modification of fusion rules, and the other is the data has been preprocessed. Inspired by Deng et al. [40]., we adopt preprocessing of the information to solve this problem.

When using LOS and ROS, it is often necessary to know which RPS are of greater importance to make rational use of the formula.However, if the two RPS are the same, there is no choice between LOS and ROS, because the result will be the same regardless of which formula is used. As for the choice of the importance of RPS, we can judge by the known information, but in addition, the idea is that for the same Y, the higher the support of an RPS from another RPS, the more reliable it is, on the contrary, the lower the support of an RPS, the less reliable it is. Based on the above discussion, we propose the following method to perform the combined Permutation Mass Function based on the distance between RPS.

However, the information quality of RPS obtained by distance weighting is often not very high, so we adopt the idea of weighted subset RPS, that is, we hope to find the weighted RPS with the highest information quality by comparing the entropy of RPS obtained by various weighted combinations. Based on the above ideas, we obtain a method combining RPS that can handle extreme conflict situations and make the information quality relatively high.

**Definition** 4.1 (Similarity matrix of RPS). The similarity matrix generated by $k$  Random Permutation Sets defined on a fixed set Y is defined by the following matrix:

$$SM_{RPS}=\left[\begin{array}{ccc}\text {Sim}\left(RPS_{1},RPS_{1}\right)&\cdots &\text {Sim}\left(RPS_{1},RPS_{k}\right)\\ :&:&:\\ \text {Sim}\left(RPS_{k},RPS_{1}\right)&\cdots &\text {Sim}\left(RPS_{k},RPS_{k}\right)\end{array}\right]\tag{18}$$

<!-- 6 -->

<!-- L.Li,P.Rong and M.Li Information Sciences 692 (2025) 121657 -->

Where Sim(RPS1, $\left.RPS_{j}\right)$  represents the similarity between two Random Permutation Sets, $RPS_{i}$  and $RPS_{j}$ ,and it is computed using the following formula:

$$\text {Sim}\left(RPS_{i},RPS_{j}\right)=1-d\left(RPS_{i},RPS_{j}\right).\tag{19}$$

In the above formula, $d\left(RPS_{1},RPS_{2}\right)$ represents the distance between $RPS_{1}$ and RI $PS_{2},$  for details, refer to Equation (14). $SM_{RPS}$ is a symmetric matrix, and since the similarity of the same RPS to itself is 1, the diagonal elements of S1 $M_{RP}$ are all 1.

**Definition** **4.2** (Support degree of RPS). For $k$ Random Permutation Sets, the support degree corresponding to each of RPS we use the following formula:

$$\text {Sup}\left(RPS_{i}\right)=\sum _{j=1,j\neq i}^{k}SM_{RPS}(i,j)\tag{20}$$

When the difference between an RPS and other RPS is small, or the distance between the RPS is small, the support degree tends to be large.

Definition 4.3 (Credibility degree of RPS). For $k$  Random Permutation Sets, the credibility degree corresponding to each of RPS we use the following formula:

$$\text {Cre}_{i}=\frac {\text {Sup}\left(RPS_{i}\right)}{\sum _{i=1}^{k}\text {Sup}\left(RPS_{i}\right)}.\tag{21}$$

The credibility degree is essentially the normalization of the support degree, so it is the same as the support degree, when an RPS is closer to other RPS, the trust degree is greater, and vice versa.

**Definition** 4.4 (Weighted PMF of subset). Following the definition of the credibility degree, the weighted PMF of subset, denoted as $RPS_{WSk}$ is presented as:

$$RPS_{WSk}=\frac {\sum _{i=1}^{n,n\geq 2}\left(Cre_{i}\times \mathcal {M}_{i}\right)}{\sum _{i=1}^{n,n\geq 2}Cre_{i}}\tag{22}$$

Where $k$  represents the $k$ -th weighted PMF of subset. If there are N PMFs in total, the maximum value of $k$  is $\sum _{k=0}^{N}\frac {N!}{(N-k)!}-N.$ The purpose of setting $n$ to 2 or greater is to prevent some extreme noise, such as the possibility of an RPS with a small entropy but providing a source of incorrect information. When $n$  is greater than or equal to 2, the existence of the weighting coefficient prevents extremely unreasonable RPS from being used for fusion. To simplify comprehension, let's illustrate the concept with a specific example.

**Example** 4.1. For a given finite set of mutually exclusive elements, Y, there are three Random Permutation Sets defined on it. In addition, the credibility degree of these three RPS are $\text {Cre}_{1},$ $\mathrm {Cre}_{2}$ and C $\mathrm {Cre}_{3}$ . The specific values of these quantities are as follows:

$$RPS_{1}=\{<(A),0.4>,<(B,C),0.2>,<(C,B),0.2>,<(D),0.2>\},$$

$$RPS_{2}=\{<(A),0.3>,<(B,C),0.3>,<(C,B),0.3>,<(D),0.1>\},$$

$$RPS_{3}=\{<(A),0.1>,<(B,C),0.2>,<(C,B),0.2>,<(D),0.5>\}.$$

$$\text {Cre}_{i}:\quad Cre_{1}=0.4,\quad \text {Cre}_{2}=0.4,\quad \text {Cre}_{3}=0.2.$$

So the specific representation of weighted PMF of subset of the three RPSs is as follows:

$$RPS_{WS1}=\frac {Cre_{1}\times \mathcal {M}_{1}\left(A_{i}\right)+Cre_{2}\times \mathcal {M}_{2}\left(A_{i}\right)}{Cre_{1}+Cre_{2}}$$

$$=\{<(A),0.35>,<(B,C),0.25>,<(C,B),0.25>,<(D),0.15>\};$$

$$RPS_{WS2}=\frac {Cre_{1}\times \mathcal {M}_{1}\left(A_{i}\right)+Cre_{3}\times \mathcal {M}_{3}\left(A_{i}\right)}{Cre_{1}+Cre_{3}}$$

$$=\{<(A),0.30>,<(B,C),0.20>,<(C,B),0.20>,<(D),0.30>\};$$

$$RPS_{WS3}=\frac {Cre_{2}\times \mathcal {M}_{2}\left(A_{i}\right)+Cre_{3}\times \mathcal {M}_{3}\left(A_{i}\right)}{Cre_{2}+Cre_{3}}$$

$$=\{<(A),0.23>,<(B,C),0.27>,<(C,B),0.27>,<(D),0.23>\};$$

$$RPS_{WS4}=\frac {Cre_{1}\times \mathcal {M}_{1}\left(A_{i}\right)+Cre_{2}\times \mathcal {M}_{2}\left(A_{i}\right)+Cre_{3}\times \mathcal {M}_{3}\left(A_{i}\right)}{Cre_{1}+Cre_{2}+Cre_{3}}$$

$$=\{<(A),0.30>,<(B,C),0.24>,<(C,B),0.24>,<(D),0.22>\};$$

<!-- 7 -->

<!-- L.Li,P.Rong and M.Li Information Sciences 692 (2025)121657 -->

**Table 4**

The results corresponding to Steps 2.


| $RPS_{i}$ | $RPS_{1}$ | $RPS_{2}$ | $RPS_{3}$ | $RPS_{4}$ |
| --- | --- | --- | --- | --- |
| $\text {Sup}\left(RPS_{i}\right)$ | 2.2847  | 2.0113  | 2.3831  | 1.9399  |


**Table 5**

The results corresponding to Steps 3.


| $RPS_{i}$ | $RPS_{1}$ | $RPS_{2}$ | $RPS_{3}$ | $RPS_{4}$ |
| --- | --- | --- | --- | --- |
| $\text {Cre}_{i}$ | 0.2651  | 0.2334  | 0.2765  | 0.2251  |


**Definition** **4.5** (Minimum entropy subset RPS). For each weighted PMF of subset, we will calculate its entropy value. Among them, the weighted RPS with the minimum entropy is defined as the minimum entropy subset RPS, $RPS_{}$ and the mathematical expression of the specific process is as follows:

RP3 $S_{me}=RPS_{WSk}$  (23)

s.t. $H_{RPS_{WSk}}=min\{H_{RPS_{WS1}},H_{RPS_{WS2}},\ldots ,H_{RPS_{WSn}}\}.$  (24)

Where $H_{RPS}$ can be obtained from Equation (16). If there are $n$ original RPSs for fusion, then when the **RPS** $S_{}$  is obtained,the final fusion result can be obtained by fusing the $RPS_{me}(n-1)$ times using LOS and ROS.

The choice of this fusion approach is primarily based on the following considerations. First, since extreme conflicts often lead to counterintuitive fusion results, we address this issue by calculating a weighted coefficient based on the distance between RPSs,which then produces a weighted RPS that effectively resolves these conflicts. However, real-world information frequently contains substantial uncertainty, and the distance between RPSs representing uncertain states tends to be relatively small. This can increase information uncertainty and slow the convergence rate ofevidence. The main objective of information fusion is to reduce the uncer-tainty in disparate sources, enabling more precise and informed decision-making. In line with this objective, we aim to identify the weighted subset RPS with minimum entropy, thereby not only handling extreme conflicts but also enhancing the overall quality of the information.

## 5. Numerical example

In this section, we shall illustrate the efficacy of our proposed methodology in addressing extreme conflict scenarios and enhancing the quality of information through specific exemplifications.

**Example** 5.1. For a given finite set of mutually exclusive elements, $\mathbb {Y}=\{A,B,C,D\},$ there are four Random Permutation Sets defined on it, which are expressed as follows:

$$RPS_{1}=\{<(A),0.3>,<(B,C),0.3>,<(C,B),0.3>,<(D),0.1>\},$$

$$RPS_{2}=\{<(A),0.6>,<(B,C),0.2>,<(C,B),0.1>,<(D),0.1>\},$$

$$RPS_{3}=\{<(A),0.3>,<(B,C),0.3>,<(C,B),0.2>,<(D),0.2>\},$$

$$RPS_{4}=\{<(A),0.1>,<(B,C),0.2>,<(C,B),0.2>,<(D),0.5>\}.$$

For the purpose of facilitating a better comprehension of the methodology proposed in this paper, we will employ the following example to delineate specific procedural steps:

Step.1. Construct the similarity matrix, the similarity between $RPS_{i}$  and $RPS_{j}$ is computed using Equation (18), where the $d\left(RPS_{i}RPS_{j}\right)$ is obtained from Equation (14). The specific outcomes are as follows:

$$\left[\begin{array}{cccc}Sim\left(RPS_{1},RPS_{1}\right)&\cdots &Sim\left(RPS_{1},RPS_{k}\right)\\ :&\cdots &:\\ Sim\left(RPS_{k},RPS_{1}\right)&\cdots &Sim\left(RPS_{k},RPS_{k}\right)\end{array}\right]=\left[\begin{array}{ccc}1&0.7219&0.9000&0.66628\\ 0.7219&1&0.7477&0.5417\\ 0.9000&0.7477&1&0.7354\\ 0.6628&0.5417&0.7354&1\end{array}\right]$$

Step.2. Utilizing the obtained similarity matrix of RPS, the support degree of RPS is computed. The specific results are as shown in Table 4.

Step.3. Furthermore, by computing the obtained support degrees, we can further derive the credibility degree of RPS. The specific results are as shown in Table 5.

<!-- 8 -->

<!-- L.Li,P.Rong and M.Li Information Sciences 692 (2025) 121657 -->

**Table 6**

The results corresponding to Steps 4 and 5.


| $RPS_{WSk}$ | $RPS_{WS1}$ | $RPS_{WS2}$ | $RPS_{WS3}$ | $RPS_{WS4}$ |
| --- | --- | --- | --- | --- |
| (A)  | 0.4405  | 0.3000  | 0.2082  | 0.3903  |
| (B,C)  | 0.2532  | 0.3000  | 0.2541  | 0.2699  |
| (C,B)  | 0.2064  | 0.2489  | 0.2541  | 0.2041  |
| (D)  | 0.1511  | 0.2334  | 0.2837  | 0.1357  |
| $H_{RPS_{WSk}}$ | 2.8545  | 3.0083  | 2.7706  | 2.8901  |
| $RPS_{WSk}$ | $RPS_{WS5}$ | $RPS_{WS6}$ | $RPS_{WS7}$ | $RPS_{WS8}$ |
| (A)  | 0.3346  | 0.2413  | 0.3340  | 0.3250  |
| (B,C)  | 0.2366  | 0.2706  | 0.2376  | 0.2542  |
| (C,B)  | 0.2044  | 0.2346  | 0.1682  | 0.2032  |
| (D)  | 0.2244  | 0.2535  | 0.2601  | 0.2177  |
| $H_{RPS_{WSk}}$ | 2.8545  | 3.0083  | 2.7706  | 2.8901  |


**Table 7**

The results corresponding to Steps $6.$ 


| $\mathcal {M}\left(A_{i}\right)$ | $M(A)$ | $\mathcal {M}(B,C)$ | $M(C,B)$ | $M(D)$ |
| --- | --- | --- | --- | --- |
| $LOS$ | 0.2819  | 0.3597  | 0.2546  | 0.1037  |
| ROS  | 0.2819  | 0.3597  | 0.2546  | 0.1037  |


**Table 8**

The specific results of RPS fusion in Example 5.2.


| $\mathcal {M}\left(A_{i}\right)$ | $M(A)$ | $\mathcal {M}(B,C)$ | $M(C,B)$ | $M(D)$ |
| --- | --- | --- | --- | --- |
| $RPS_{1}\stackrel {\leftarrow }{\oplus }RPS_{2}\stackrel {\leftarrow }{\oplus }RPS_{3}$ |  |  |  |  |
|  | 0  | 0.5000  | 0.5000  | 0  |
| $RPS_{1}\oplus RPS_{3}\oplus RPS_{2}$ | 0  | 0.5000  | 0.5000  | 0  |
| $RPS_{2}\oplus [\leftarrow ]}RPS_{1}\oplus [\leftarrow ]}RPS_{3}$ | 0  | 0.5000  | 0.5000  | 0  |
| $RPS_{2}\oplus RPS_{3}\oplus RPS_{1}$ $RPS_{3}\oplus RPS_{1}\oplus RPS_{2}$ | 0  | 0.5000  | 0.5000  | 0  |
| $RPS_{3}\oplus RPS_{2}\oplus RPS_{1}$ | 0  | 0.5000  | 0.5000  | 0  |
|  | 0  | 0.5000  | 0.5000  | 0  |
| $RPS_{me}\oplus RPS_{me}\oplus RPS_{me}$ | 0.9981  | 0.0008  | 0.0008  | 0.0002  |


Step.4. Calculate the weighted subset RPS. The specific results are as shown in Table 6.

Step.5. Compute the entropy values for each weighted subset RPS and identify the weighted subset of RPS corresponding to the minimum entropy value. The specific results are as shown in Table 6 and $RP_{m}=RP_{W7}$ 

Step.6.The specific result of fusing ROS and LOS $n-1$ times using the identified minimum entropy subset RPS are as shown in Table 7.

**Example** 5.2. For a given finmite set of mutually exclusive elements, $\mathbb {Y}=\{A,B,C,D\}$ ,there are three Random Permutation Sets defined on it, which are expressed as follows:

$$RPS_{1}=\{<(A),0>,<(B,C),0.05>,<(C,B),0.05>,<(D),0.9>\},$$

$$RPS_{2}=\{<(A),0.9>,<(B,C),0.05>,<(C,B),0.05>,<(D),0>\},$$

$$RPS_{3}=\{<(A),0.8>,<(B,C),0.05>,<(C,B),0.05>,<(D),0.1>\}.$$

The purpose of this example is to demonstrate the performance of our newly proposed method in dealing with highly conflicting information. We utilize the original LOS, as well as our proposed method, to fuse these three RPSs separately. The results are shown in Table 8.

We first analyze the original three RPSs, and we find that there is a serious conflict between $RPS_{1}$ and RP $S_{2}$ ,while the value of RF $S_{3}$ is very close to that of R $PS_{2}.$ We can map this situation to the actual situation, for example, the data of three RPSs come from three sensors respectively, the real situation is $(A)$ ), but due to sensor damage or other reasons, the data corresponding to sensor 1,the result of $RPS_{1}$ produces the opposite result. The primary goal of information fusion is to enrich and refie the quality of available information, ultimately empowering more accurate and informed decision-making processes. In this case, if the original LOS and ROS are used, it is bound to produce results that are contrary to the real situation. As shown in Table 8, intuitively, there is more evidence pointing to a higher value of $M(A)$ , but the fusion result is 0 in the case of conflict. However, if we use our proposed method, we can handle this extreme conflict situation well.

<!-- 9 -->

<!-- L.Li,P.Rong and M.Li Information Sciences 692 (2025) 121657 -->

**Table 9**

The specific results of RPS fusion in Example 5.3.


| $\mathcal {M}\left(A_{i}\right)$ | $M(A)$ | $M(B,C)$ | $M(C,B)$ | $M(D)$ |
| --- | --- | --- | --- | --- |
| $RPS_{1}\oplus RPS_{2}\oplus RPS_{3}$ | $RPS_{1}\oplus RPS_{2}\oplus RPS_{3}$ |  |  |  |
| $RPS_{1}\stackrel {\leftarrow }{\oplus }RPS_{3}\stackrel {\leftarrow }{\oplus }RPS_{2}$ | 0  | 0.8365  | 0  | 0.1635  |
| $RPS_{2}\stackrel {\leftarrow }{\oplus }RPS_{1}\stackrel {\leftarrow }{\oplus }RPS_{3}$ | 0  | 0.8365  | 0  | 0.1635  |
| $RPS_{2}\stackrel {\leftarrow }{\oplus }RPS_{3}\stackrel {\leftarrow }{\oplus }RPS_{1}$ | 0  | 0.0092  | 0.8274  | 0.1635  |
| $RPS_{3}\oplus RPS_{1}\oplus RPS_{2}$ | 0  | 0.0092  | 0.8274  | 0.1635  |
| $RPS_{3}\oplus RPS_{2}\oplus RPS_{1}$ | 0  | 0.0204  | 0.8162  | 0.1634  |
|  | 0  | 0.0204  | 0.8162  | 0.1634  |
| $RPS_{me}\oplus RPS_{me}\oplus RPS_{me}$ | 0.9425  | 0.0022  | 0.0529  | 0.0024  |


**Table 10**

The specific results of RPS fusion in Example 5.4.


| $\mathcal {M}\left(A_{i}\right)$ | $\mathcal {M}(A,B,C)$ | $M(A,C,B)$ | $M(B,A,C)$ | $M(D)$ |
| --- | --- | --- | --- | --- |
| $RPS_{1}\oplus RPS_{2}\oplus RPS_{3}$ | $0.9472$ |  |  |  |
|  |  | 0  | 0.0526  | 0.0001  |
| $RPS_{1}\oplus RPS_{3}\oplus RPS_{2}$ | 0.9472  | 0  | 0.0526  | 0.0001  |
| $RPS_{2}\oplus RPS_{1}\oplus RPS_{3}$ $RPS_{2}\stackrel {\leftarrow }{\oplus }RPS_{3}\stackrel {\leftarrow }{\oplus }RPS_{1}$ | 0  | 0.9472  | 0.0526  | 0.0001  |
|  | 0  | 0.9472  | 0.0526  | 0.0001  |
| $RPS_{3}\oplus RPS_{1}\oplus RPS_{2}$ $RPS_{3}\oplus RPS_{2}\oplus RPS_{1}$ | 0.5262  | 0.4210  | 0.0526  | 0.0001  |
|  | $0.5262$ | 0.4210  | 0.0526  | 0.0001  |
| $RPS_{me}\oplus$ $RPS_{me}\oplus RPS_{me}$ | 0.7129  | 0.2343  | 0.0526  | 0.0001  |


**Example** 5.3. For a given finite set of mutually exclusive elements, $\mathbb {Y}=\{A,B,C,D\}$ ,there are three Random Permutation Sets defined on it, which are expressed as follows:

$$RPS_{1}=\{<(A),0.9>,<(B,C),0.01>,<(C,B),0>,<(D),0.09>\},$$

$$RPS_{2}=\{<(A),0>,<(B,C),0.01>,<(C,B),0.9>,<(D),0.09>\},$$

$$RPS_{3}=\{<(A),0.5>,<(B,C),0.01>,<(C,B),0.4>,<(D),0.09>\}.$$

The aim of this example is to investigate the performance of both the newly proposed method and the original direct utilization of LOS and ROS in a conflict scenario akin to Example 3.2. The detailed outcomes are presented in Table 9.

Through this example, we can find that although two of the three RPSs have high values for $\mathcal {M}(A)$ , the final fusion result is 0 due to the existence of Ri $PS_{2},$  which is certainly not consistent with our intuition. However, the relative values of $M(C,B)$  and $M(B,C)$ will produce opposite results due to the influence of fusion direction. However, through direct analysis of the original RPSs, we can find that $M(B,C)$  has always been at a low level, while $M(C,B)$  is at a high level. However, due to the existence of M(C,E $B=0$ in $RPS_{1}$ ,the value of $M(C,B)$  may be $0$  after fusion, which is also quite different from the results of our intuitive analysis. However,if we use the proposed method to analyze, it can be found that $M(A)$  is much larger than $M(D)$  for the case of a single event, and $M(C,B)$  is much larger than $M(B,C)$  for the case of two events, which conforms to our intuitive feeling.

**Example** 5.4. For a given finite set of mutually exclusive elements, $\mathbb {Y}=\{A,B,C,D\}$ ,there are three Random Permutation Sets defined on it, which are expressed as follows:

$$RPS_{1}=\{<(A,B,C),0.9>,<(A,C,B),0>,<(B,A,C),0.05>,<(D),0.05>\},$$

$$RPS_{2}=\{<(A,B,C),0>,<(A,C,B),0.9>,<(B,A,C),0.05>,<(D),0.05>\},$$

$$RPS_{3}=\{<(A,B,C),0.5>,<(A,C,B),0.4>,<(B,A,C),0.05>,<(D),0.05>\}.$$

The objective of this example is to investigate the efficacy of the newly proposed method compared to the direct utilization of LOS and ROS in scenarios akin to Example 3.3, where conflicts arise. The specific results are delineated in Table 10.

Through the calculation, we can find that when the fusion order is changed, it will have a great impact on the final fusion result.Of the above three RPSs, $M(A,B,C)$  and $M(A,C,B)$  are at a large value in most cases, but if an RPS with $\mathcal {M}\left(A_{i}\right)=0$ is used as the last item in the fusion, the value will be set to zero no matter how much support the other RPS have for it. This obviously defeats the purpose of combining multiple information. Nonetheless, by employing the method delineated in this paper, we effectively address this issue.

**Example** 5.5. For a given finite set of mutually exclusive elements, $\mathbb {Y}=\{A,B,C\}$ , there are three Random Permutation Sets defined on it, which are expressed as follows:

<!-- 10 -->

<!-- L.Li,P.Rong and M.Li Information Sciences 692 (2025) 121657 -->

**Table 11**

The specific results of RPS fusion in Example 5.5.


| $\mathcal {M}\left(A_{i}\right)$ | $M(A)$ | $M(A,B)$ | $M(B,A)$ | $M(C)$ |
| --- | --- | --- | --- | --- |
| $RPS_{1}\stackrel {\leftarrow }{\oplus }RPS_{2}\stackrel {\leftarrow }{\oplus }RPS_{3}$ | $RPS_{1}\stackrel {\leftarrow }{\oplus }RPS_{2}\stackrel {\leftarrow }{\oplus }RPS_{3}$ |  |  |  |
| $RPS_{1}\stackrel {\leftarrow }{\oplus }RPS_{3}\stackrel {\leftarrow }{\oplus }RPS_{2}$ | 0.7036  | 0.1481  | 0.1481  | 0.0001  |
| $RPS_{2}\stackrel {\leftarrow }{\oplus }RPS_{1}\stackrel {\leftarrow }{\oplus }RPS_{3}$ | 0.7036  | 0.1481  | 0.1481  | 0.0001  |
| $RPS_{2}\stackrel {\leftarrow }{\oplus }RPS_{3}\stackrel {\leftarrow }{\oplus }RPS_{1}$ | 0.7036  | 0.1481  | 0.1481  | 0.0001  |
| $RPS_{3}\oplus RPS_{1}\oplus RPS_{2}$ | 0.7036  | 0.1481  | 0.1481  | 0.0001  |
| $RPS_{3}\oplus RPS_{2}\oplus RPS_{1}$ | 0.7036  | 0.2469  | 0.0494  | 0.0001  |
|  | 0.7036  | 0.2469  | 0.0494  | 0.0001  |
| $RPS_{me}\oplus RPS_{me}\oplus RPS_{me}$ | 0.7035  | 0.1940  | 0.1023  | 0.0002  |


**Table 12**

The original three RPS data.


| Permutation events  | (A)  | (B)  | (C)  | (A,B)  | (B,A)  | (A,C)  |
| --- | --- | --- | --- | --- | --- | --- |
| $RPS_{1}$ | 0.2  | 0.08  | 0  | 0.12  | 0.05  | 0.03  |
| $RPS_{2}$ | 0.07  | 0.13  | 0.02  | 0.07  | 0.2  | 0.1  |
| $RPS_{3}$ | 0.14  | 0.09  | 0  | 0.12  | 0.08  | 0  |
| Permutation events  | (C,A)  | (A,B,C)  | (A,C,B)  | (B,A,C)  | (B,C,A)  | $(C,A,B)$ |
| $RPS_{1}$ | 0  | 0.12  | 0.25  | 0.1  | 0.05  | 0  |
| $RPS_{2}$ | 0  | 0.13  | 0  | 0.2  | 0  | 0.08  |
| $RPS_{3}$ | 0  | 0.12  | 0.3  | 0.1  | 0  | 0.05  |


$$RPS_{1}=\{<(A),0.3>,<(A,B),0.3>,<(B,A),0.3>,<(C),0.1>\},$$

$$RPS_{2}=\{<(A),0.33>,<(A,B),0.33>,<(B,A),0.33>,<(C),0.01>\},$$

$$RPS_{3}=\{<(A),0.3>,<(A,B),0.5>,<(B,A),0.1>,<(C),0.1>\}$$

Through this example, we aim to analyze the performance of our proposed method compared to the direct use of LOS and ROS in the face of more information sources with lower information quality. Its specific fusion results can be seen in Table 11.

We analyze the original three RPSs, and the information of $RPS_{1}$ and RM $S_{2}$  tells us the same information, that is, $(A,B)$  and $(B,A)$  have the same assignment, in other words, RI $S_{1}$ and **RF** $PS_{2}$ cannot judge which of $(A,B)$  and $(B,A)$  has the higher weight.But the information of RP. $S_{3}$  tells us that the weight of $(A,B)$  is originally greater than that of $(B,A)$ . According to the previous literature, we can find that the fusion order will greatly affect the final fusion result. One method is to use the distance between

RPSs to obtain the weight and further judge the fusion order. If this idea is used, then the way of fusion is RF $PS_{1}\oplus RPS_{2}\oplus$ $RPS_{3}$ However, according to Table 11, we can find that it cannot judge which of $(A,B)$  and $(B,A)$  has the higher weight. The reason for this result is that the RPS closest to other RPS is often not the one with the highest and most reasonable quality, but the one with low information quality and relatively imprecise information. However, using the method we propose in this paper, the quality of the fusion results can be improved.

## 6.Experiment

### 6.1. Brief introduction

In this section, we endeavor to apply the fusion methodology proposed in this paper to enhance the reliability ranking of threat assessments. We consider a practical scenario involving the detection of enemy aircraft, as elucidated in Reference [41]. Here,multiple Random Permutation Sets are derived from detection reports provided by various radar systems. We assume the existence of three distinct types of aircraft, denoted by $\mathbb {Y}=\{A$ ,B,C},each representing potential threat targets. The permutation event space $PES(Y)$ comprehensively encompasses all conceivable threat events, where the order within the permutation event signifies the relative threat ranking. For detailed problem specifications, readers are referred to Reference [41]. Our primary focus lies in evaluating the effectiveness of the proposed method. To facilitate subsequent analysis, we summarize the original configurations of RPSs outlined in the literature in Table 12. Utilizing the method proposed in this paper, we systematically navigate through the steps illustrated in Fig. 1 to address the complexities inherent in this threat assessment challenge.

### 6.2. Experimental results

Our analysis of the raw RPS data reveals that RF $P_{1}$ and RPS $S_{3}$ support each other, and **R** $PS_{2}$ is a relatively **terrible** report because its results are clearly not uniform with those of the other RPS. If we judge dangerous events artificially, we can obviously find that the highest threat event for one type of aircraft is (A), the highest threat event for two types of aircraft is (A, B), and the highest threat event for three types of aircraft is (A,C,B).

<!-- 11 -->

<!-- L.Li,P.Rong and M.Li Information Sciences 692 (2025) 121657 -->

<!-- (A,B,C) Our Main Work RPS sourves Step 1:Constructing similarity matrix of RPS (A),(B),(C) RPS 1 Step 2:Calculate the support degree of RPS 1-type threat events RPS 2 (A,B),(B,A), Step 3:Calculate the credibility degree of RPS RPSme $\mathrm {RPS}_{\mathrm {me}}$ (A,C),(C,A), RPS 3 (B,C),(C,B) 2-type threat events Step 4:Calculate the weighted PMF of subset (A,B,C),(B,A,C), (A,C,B),(C,A,B), Step 5:Compute the entropy values for each (B,C,A),(C,B,A) weighted PMF of subset 3-type threat events The RPSDF algorithm (?) 1-type threat events Step 6:Combine RPSs based on LOS (?,?) 2-type threat events Step 7:Compute the relative treat degree (?,?,?) Step 8:Determine the final threat assessment 3-type threat events -->
![](https://web-api.textin.com/ocr_image/external/1dc70733ab3e7e81.jpg)

Fig.1. Flowchart of risk assessment using the proposed method in this paper.

**Table 13**

Entropy of the weighted subset RPS.


| $RPS_{WSk}$ | $RPS_{WS1}$ | $RPS_{WS2}$ | $RPS_{WS3}$ | $RPS_{WS4}$ |
| --- | --- | --- | --- | --- |
| $H_{RPS}$ | 5.6164  | 5.4580  | 5.6314  | 5.6233  |


**Table 14**

The fusion results of PMFs and the relative threat degree for different permutation events in threat assessment.


| Permutation events  | (A)  | (B)  | (C)  | (A,B)  | (B,A)  | (A,C)  |
| --- | --- | --- | --- | --- | --- | --- |
| Fusion results  | 0.3955  | 0.1659  | 0  | 0.1775  | 0.0698  | 0.0140  |
| Relative threat degree  | 0.7045  | 0.2955  | 0  | 0.6770  | 0.2662  | 0.0534  |
| Permutation events  | (C,A)  | (A,B,C)  | (A,C,B)  | (B,A,C)  | (B,C,A)  | (C,A,B)  |
| Fusion results  | 0.0009  | 0.0388  | 0.0889  | 0.0323  | 0.0081  | 0.0081  |
| Relative threat degree  | 0.0034  | 0.2202  | 0.5045  | 0.1833  | 0.0460  | 0.0460  |


Fortunately, our algorithm can determine which combination is beneficial to improve the quality of the information and exclude the RPS that contradict the information of other RPS. As shown in Table 13, we find that RP $P_{W2}$ possesses the minimum entropy and its corresponding combination comes from $RPS_{1}$  and $RPS_{3}$ ,, which is consistent with our analysis results in the previous paragraph.In addition, in order to weaken the influence of RPS that contradicts other RPS, a method has been proposed by previous researchers.That is, in reference [41], a relatively reasonable fusion result can be obtained by changing the fusion order by judging the distance between RPSs. This idea fully reflects that the fusion order of RPS has a huge impact on the final result. However, there may be two potential problems in doing so. The first one is that the RPS which is far away from other RPS may be a huge noise, that is, it may have serious result offset. If the utilization of this RPS persists in fusion processes, it could potentially lead to significant discrepancies, as detailed in Section 3. In addition, the second problem is that the RPS with imprecise information is often the closest to the sum of the distances between each RPS. If it is taken as the most important item for fusion, the fusion result may be uncertain, which is not conducive to our decision-making and judgment.

We show our final fusion results and the relative danger degree of each permutation event in Table 14. By observing the table,we can find that the permutation event with the highest threat degree of each type is consistent with the results of our preliminary analysis. For comparison, the fusion results in different orders by directly using LOS and the fusion results by using the method proposed in this paper are respectively made. The specific results are shown in Fig. 2, Fig. 3 and Fig. 4, and the corresponding fusion order of different cases is shown in Table 15.

Through the analysis of Fig. 2, Fig. 3 and Fig. 4, we find that the expected results can be obtained by using literature [41]and the method in this paper, that is, (A), (A,B), (A,C,B) are respectively the highest values of threat assessment in different situations.It has already been mentioned in the previous sections, in addition to this, our approach can face some peculiar cases, including low information quality and information facing extreme conflicts. In addition, we find that the results of different fusion sequences mainly depend on the RPS of the first fusion, but giving too high weight to one RPS often leads to one-sided information, thus losing the

<!-- 12 -->

<!-- L.Li,P.Rong and M.Li Information Sciences 692 (2025) 121657 -->

Table 15

The fusion order corresponding to different cases.


| Case 1<br>$RPS_{1}\stackrel {\leftarrow }{\oplus }RPS_{2}\stackrel {\leftarrow }{\oplus }RPS_{3}$ | Case 2<br>$RPS_{1}\stackrel {\leftarrow }{\oplus }RPS_{3}\stackrel {\leftarrow }{\oplus }RPS_{2}$ | Case 3<br>$RPS_{2}\overleftarrow {\oplus }RPS_{1}\overleftarrow {\oplus }RPS_{3}$ |
| --- | --- | --- |
| Case 4<br>$RPS_{2}\oplus RPS_{3}\oplus RPS_{1}$ | Case 5<br>$RPS_{3}\stackrel {\leftarrow }{\oplus }RPS_{1}\stackrel {\leftarrow }{\oplus }RPS_{2}$ | Case 6<br>$RPS_{3}\oplus$ $RPS_{2}\oplus$PS1  |


<!-- (A) (B)(C) 0.8 0.6 0.4 0.2 0 case 4 case 1 case2 case3 case 5 case 6 presentedmethod -->
![](https://web-api.textin.com/ocr_image/external/f2c9892255c0d81d.jpg)

Fig. 2. The ultimate threat assessment for a specific aircraft type.

original purpose of multi-source information fusion. However, our proposed selection of the weighted subset PMF with the minimum entropy to fuse $n-1$  times not only considers the information provided by more RPSs, but also does not cause the results to change greatly due to the change of fusion order. We also calculate the entropy of case 1 and case 2, which directly use LOS and obtain the best results, and the entropy of the final RPS, which is calculated by the proposed method. The entropy value of the former is 3.7770while that of our method is 3.7439. This also proves our method is beneficial in improving the quality of the RPS obtained by the final fusion.

In summary, in this ideal experiment, we used classical LOS and ROS to fuse RPSs, and the optimal results obtained were consistent with those achieved by our method. However, the entropy of the final fusion result from our approach is lower, and compared to enumerating all possible combinations, our method reduces computational cost to some extent. Experimental results demonstrate that our method offers certain advantages in terms of result accuracy, computational complexity, and result uncertainty.

### 6.3. A classification algorithm based on the RPS fusion method proposed in this paper

In this section, we integrate the proposed method with the PMF generation approach outlined in [45] to develop an algorithm capable of classifying real-world datasets.

Similar to the evidence theory, thegeneration of Permutation Mass Functions (PMFs) in Random Permutation Set plays a pivotal role in applying this theoretical framework to practical computer science applications. In [45], the basic probability assignment function is initially generated using a Gaussian distribution, and the distance between the attribute value and the sample mean is subsequently utilized to refine this function, culminating in the final PMF. This study extends this PMF generation approach by integrating it with the distance and entropy fusion techniques we developed, enabling successful classification across several real-world datasets. For a comprehensive explanation of the PMMF generation process, readers are referred to [45], where the methodology is thoroughly detailed. However, as the primary focus of this paperlies elsewhere, we will not delve into the specifics of the PMF generation here, in order to maintain coherence and conciseness. We only give the relevant calculation formulas here.

The overall algorithm can be divided into three steps: Generate PMFs, Fusion, and Classify. The detailed flowchart is shown in Fig. 5, while the corresponding pseudocode is provided in Algorithm 1.

The specific algorithm implementation is detailed in the following steps.

**Step.1.**:Generate Permutation Mass Functions.

<!-- 13 -->

<!-- L.Li,P.Rong and M.Li Information Sciences 692 (2025) 121657 -->

<!-- (A,B) (B,A) (A,C) (C,A) 0.6 0.4 0.2 0 case 2 case 1 case case 4 case 5 case 6 presented method -->
![](https://web-api.textin.com/ocr_image/external/f15c6189b3ec8729.jpg)

Fig. 3. The ultimate threat assessment for two aircraft types.

<!-- (A,B,C) (A,C,B) (B,A,C) (B,C,A) (C,A,B) $C,A,B)$ 0.5 0.4 0.3 0.2 0.1 0 $5$ case1 case 2 case3 case 4 case  case 6 presented method -->
![](https://web-api.textin.com/ocr_image/external/7d2e1025dc6585b1.jpg)

Fig. 4. The ultimate threat assessment for three aircraft types.

(i) Calculate the mean and variance of each attribute for each category using Equation (25) and Equation (26), where $N_{i}$ represents the number of training samples for the $i-th$  category,and $x_{ij}^{(l}$  denotes the $j-th$ sample of the $i$ -th category,

$$\bar {x}_{ij}=\frac {1}{N_{i}}\sum _{l=1}^{N_{i}}x_{ij}^{(l)},\tag{25}$$

$$σ_{ij}=\sqrt {\frac {1}{N_{i}-1}\sum _{l=1}^{N_{i}}\left(x_{ij}^{(l)}-\bar {x}_{ij}\right)^{2}}\tag{26}$$

(ii) Construct a Gaussian discriminant model (GDM) for each attribute corresponding to each category using Equation (27),where $\bar {x}_{ij}$ represents the previously calculated mean, and $σ_{ij}$ denotes the calculated variance,

<!-- 14 -->

<!-- L.Li,P.Rong and M.Li Information Sciences 692 (2025) 121657 -->

#### Algorithm 1 A classification algorithm based on distance and entropy of Random Permutation Set.

Input: Training samples $\mathbf {X}_{ixj_{1}}$ and corresponding labels $\mathbf {C}_{\text {train}}=\left\{C_{1},C_{2}\ldots C_{k}\right\}$ ,testing samples $\mathbf {Y}_{i\times j_{2}}$ 

Output: Classification result $\mathbf {C}_{\text {out}}$ 

**Generate PMFs:**

for each label $\mathbf {C}_{\text {train}}$ in X do

for each attribute $i$  in $\mathbf {C}_{\text {train}}\text {do}$ 

**for** **each** sample $j_{1}$ in i do

Obtain the sample mean and variance using Eq. (25), Eq.(26);

Construct the GDM for each class and each attribute using Eq.(27);

end for

**end** for

end **for**

for each sample $j_{2}$ in Y do

for each attribute i in $j_{2}$  do

Combine the probability density values of each class into a single vector using Eq.(31);

Sort the probability density values and obtain the indices of the sorted values using Eq. (32).;

Populate the probabilities of the power set based on the sorting results;

Incorporating sample mean distance information to enhance permutation data and generate PMF using Eq. (33), Eq. (34) and Eq. (35) Eq.(36);

end for

**end for**

**Fusion:**

for each sample $n_{2}$ in $Y$  do

for each PMF m in $n_{2}$  do

Compute the distance between the PMF and other PMFs using Eq.(14);

end for

**end for**

Compute the similarity matrix using Eq.(18);

Compute the credibility degree of each RPS using Eq.(21);

Compute the weighted PMF of subset;

for each weighted PMF of subset do

Compute the entropy for each weighted PMF of subset using Eq.(22);

**end for**

**for** **each** weighted PMF of subset do

find the minimum entropy subset RPS using Eq. (16);

**end for**

fusion the minimum entropy subset RPS using Eq. $(7);$ 

**Classify:**

**for each** sample $n_{2}$ in Y do

Calculate the distance between PMFs $D_{m}$ using Eq.(14);

Determine the minimum distance sum and obtain the result $\mathbf {C}_{\text {out}};$ 

**end for**

$$f_{ij}(x)=\frac {1}{\sqrt {2\pi {\sigma _{ij}}^{2}}}\exp \left(-\frac {\left(x-\bar {x}_{ij}\right)^{2}}{2{\sigma _{ij}}^{2}}\right)\tag{27}$$

(iii) Construct associated membership vector for each attribute using Equation (28). In the formula, $f_{ij}$ represents the Gaussian distribution function model generated for the $j-th$ attribute of the i-th class,

$$\mathbf {f}_{j}=\left[f_{ij}\vert i=1,2,\cdots ,N\right].\tag{28}$$

(iv) Calculate the membership degree and normalize the membership vector using Equation (29) and Equation (30). In Equation (29), $x_{0j}$ represents the $j-th$ attribute value of the test sample $x_{0}$ in the test dataset,

$$\left.f_{ij}\triangleq f_{ij}(x)\right|_{x=x_{0j}}.\tag{29}$$

$$\tilde {f}_{ij}=\frac {f_{ij}}{\sum _{i}f_{ij}}.\tag{30}$$

And the normalized membership vector (NMV) is expressed as follows,

$$\tilde {\mathbf {f}}_{j}=[\tilde {f}_{ij}\vert i=1,2,...,N].\tag{31}$$

(v) Rank the elements $\left.\tilde {f}_{ij}\right|_{i=1}^{N}$  of the $j-th$  normalized membership vector in decreasing order $\left.\tilde {f}_{i_{u}j}^{Ord}\right|_{u=1}^{N},$ and ordered normalized membership vector (ONMV) is expressed as follows,

$$\tilde {\mathbf {f}}_{j}^{\text {Ord}}=\left[\tilde {f}_{i_{u}j}^{\text {Ord}}\vert u=1,2,\cdots ,N\right].\tag{32}$$

(vi) Calculate supporting degree of the $u-th$ GDM to the $j-th$ attribute using Equation (33). The closer the attribute value of a sample is to the average value of a specific category, the higher its degree of support,

<!-- 15 -->

<!-- L.Li,P.Rong and M.Li Information Sciences 692(2025)121657 -->

<!-- Dataset RPS sources/ Result Original RPSi $\text {RPS}_{i}$ ... RPSj $\mathrm {RPS}_{\mathrm {j}}$ RPSk $\mathrm {RPS}_{\mathrm {k}}$ RPS operator Dataset Concepts of order Calculate distance and Weight Intermediate data Training set Test set Matching RPS,W $\mathrm {RPS}_{\mathrm {i}}{}^{\mathrm {w}}$ ... ,W $RPS_{j}^{w}$ ... W $\mathrm {RPS}_{\mathrm {k}}{}^{w}$ $GDM$ ... GDMJ $GDM_{j}$ ... GDMk $\mathrm {GDM}_{\mathrm {k}}$ Calculate entropy and Fusion Weight analysis $f_{1}$ ... fj $f_{J}$ ... $f_{K}$ k $\mathrm {RPS}_{\mathrm {me}}$ Fusion Normalize and rank $\left.f_{j}\right|^{K}{}_{j=1}$ RPSm $\mathrm {RPS}_{\mathrm {me}}$ $\left.W_{j}\right|_{j=1}^{K}$ $f_{1}\text {Ord}$ ... $f_{3}\text {Ord}$ ... $\mathrm {f}_{\mathrm {K}}\text {Ord}$ Calculate distance $M^{w}{}_{i}$ $M^{w}{}_{j}$ $M^{w}{}_{k}$ RPS,std $\mathrm {RPS}_{\mathrm {i}}{}^{\text {std}}$ RPS,std $\mathrm {RPS}_{\mathrm {j}}{}^{\text {std}}$ ... RPSkstd $\mathrm {RPS}_{\mathrm {k}}{}^{\text {std}}$ ... ... Find the minimun distance $\mathrm {RPS}_{\mathrm {i}}$ ... RPSj $\mathrm {RPS}_{\mathrm {j}}$ ... $\mathrm {RPS}_{\mathrm {k}}$ RPSk Generate PMFs Result Classify -->
![](https://web-api.textin.com/ocr_image/external/22090b2d24cdfbc0.jpg)

Fig. 5. The main process of the algorithm.

$$s_{i_{u}j}=\exp \left(-\left|x_{0j}-\bar {x}_{i_{u}j}^{Ord}\right|\right)\tag{33}$$

(vii) Obtain weight vector $\mathbf {w}_{j}$  for the element order in the $j-th$  attribute. And the formula can be referenced in Equation (34),

$$\mathbf {w}_{j}=\left[\begin{array}{c|c}&q=1,2,\ldots ,N\\ \left.w_{\left(i_{1}\ldots i_{u}\ldots i_{q}\right.}\right)j&u=1,2,\ldots ,q\\ &\left(i_{1}\ldots i_{u}\ldots i_{q}\right)\in \operatorname {APS}(q)\end{array}\right]\tag{34}$$

where $w_{\left(i_{1}\cdots i_{u}\cdots i_{q}\right)j}$ is the weight factor which can reflect the relative importance of permutation events. In the above formula, $APS(q)$ represents the all permutation space. And the specific equation of $w_{\left(i_{1}\cdots i_{u}\cdots i_{q}\right)}$ is expressed as follows,

$$w_{\left(i_{1}\cdots i_{u}\cdots i_{q}\right)j}=\prod _{u=1}^{q}\frac {s_{i_{u}j}}{\sum _{t=u}^{q}s_{i_{t}j}}.\tag{35}$$

(viii) Calculate weighted PMF $\mathcal {M}_{j}^{\mathrm {w}}$ based on weight vector $\mathbf {w}_{j}$ and ordered normalized membership vector $\tilde {f}_{j}^{\text {Ord}},$ 

$$\mathcal {M}_{j}^{\mathrm {w}}\left(\theta _{i_{1}}^{Ord},\cdots ,\theta _{i_{u}}^{Ord},\cdots ,\theta _{i_{q}}^{Ord}\right)=\left\{\begin{array}{ll}w_{\left(i_{1}\cdots i_{u}\cdots i_{q}\right)^{j}}·\tilde {f}_{i_{q}j}^{Ord},&\left(i_{1}\cdots i_{u}\cdots i_{q}\right)\in APS(q)\\ 0,&\left(i_{1}\cdots i_{u}\cdots i_{q}\right)\notin APS(q)\end{array}\right.\tag{36}$$

And then, weighted RPS based on the $j$ -th weighted PMF is generated,

$$RPS_{j}^{\mathrm {w}}=\left\{\left\langle A,M_{j}^{\mathrm {w}}(A)\right\rangle \vert A\in PES(Θ)\right\}\tag{37}$$

##### Step.2.: Fusion Permutation Mass Functions.

(i) Obtain the similarity matrix of RPS using Equation (18)and Equation (19).

(ii) Use the similarity matrix of RPS to calculate the support degree of RPS, the formula can be referenced in Equation (20).

(iii) Calculate the credibility degree of each RPS, with the specific formula referenced in Equation (21).

(iv) Use the credibility degree of each RPS to calculate the weighted PMF of all subsets, the specific formula can be referenced in Equation (22).

(v) Calculate the entropy of the weighted PMF of all subsets and identify the RP. $S_{m}$ with the minimum entropy value. The specific formula can be referenced in Equation (23) and Equation (24).

##### Step.3.: Perform the final classification.

(i) Calculate the distance between $RPS_{me}$ and each $RPS^{st}$ , the distance formula between RPS can be referenced in Equation (14).

<!-- 16 -->

<!-- L.Li,P.Rong and M.Li Information Sciences 692 (2025) 121657 -->

<!-- 0.94 0.93 0 φ 0.92 Average accuracy 0.91 0.9 0 --Θ--average accuracy SRP divergence 0.89 average accuracy distance average accuracy Permutation JS divergence average accuracy our method 0.88 0.2 0.3 0.4 0.5 0.6 0.7 0.8 Proportion of training set -->
![](https://web-api.textin.com/ocr_image/external/b29d4e129d258a8e.jpg)

Fig. 6. Comparisons of the methods on the Iris dataset.

(ii) Compare the calculated distance values. The category corresponding to the RPSstd with the minimum distance is the final classification result.

### 6.4. Experiments and results of the classification algorithm

To validate the effectiveness of our proposed method, we employed the Iris and Wine datasets from the UC Irvine Machine Learning Repository (https://archive.ics.uci.edu)) for this experiment. To assess the performance of our integration method, we compared our approach with the fusion methods outlined in Reference [41], Reference [46], and Reference [20]. To ensure the rigor of the experi-ment, we utilized different methods solely in the fusion algorithms, while maintaining consistency in the generation of Permutation Mass Functions and the final classification algorithm. The PMF generation algorithm is based on the Gaussian distribution-based PMF generation method mentioned in Reference [45]. Classification algorithm determines the final category by calculating the distance between the generated PMF and the PMF assigned exclusively to a specific class. The final classification is established by selecting the PMF with the minimum distance.

Using the method described above, we performed classification on the Iris dataset, Seeds dataset, and Wine dataset. In the figures and tables shown below, **SRP** **divergence** represents the fusion method proposed in Reference [46], **distance** represents the fusion method proposed in Reference [41], and **Permutation JS** **divergence** represents the fusion method proposed in Reference [20]. The detailed classification results are shown in Fig. 6, Fig. 7, Table 16 and Table 17. Fig. 6 and Fig. 7 illustrate the comparison of the classification outcomes between our method and three other fusion methods, under the condition that the same PMFs are generated.Table 16 and Table 17 present the average accuracy and variance values for the four methods across different training set ratios.

Given that the Iris dataset includes four attributes, there are 24 possible permutations. In comparison, the Wine dataset,with its thirteen attributes, has 13! (13 factorial) permutations, amounting to 6,227,020,800-making it impractical to list all possible permutations. In the following analysis, we calculated results by directly applying LOS fusion on the Iris dataset, illustrating that fusion results obtained without an appropriate fusion order can be quite suboptimal. In cases where there is no prior knowledge or method to establish the fusion order, our approach proves highly effective, as it does not depend on the influence of fusion order.

From Figs. 6 and 7, it is evident that, with other factors held constant, our proposed method consistently achieves the highest accuracy among the four different fusion methods. Analyzing the three methods used for comparison, both the distance-based and Per-mutation JS divergence-based methods on Random Permutation Sets quantify the differences between RPS to derive a weighted RPS,addressing evidence conflict and extracting additional information whenever possible. The SRP divergence method, when weighting based on differences, introduces the use of the PIQ (a method for quantifying the quality of RPS) in conjunction with the weighting co-efficients derived from the differences, resulting in the final weighted RPS. This approach shares similarities with our method, as both consider information differences and quality; however, their integration methods differ. Notably, the SRP divergence is unbounded when quantifying the differences between RPS, which can lead to infinite results during further calculations and inferences using SRP divergence. This leads to situations where, when using the SRP divergence-based method, if certain permutation events of one PMF are assigned a value of 0 while the other PMF has non-zero values, infinite results can occur. Even replacing 0 with a very small value (e.g., 0.0001) can significantly impact the final outcome. In contrast, our method does not produce any infinite results and focuses

<!-- 17 -->

<!-- L.Li,P.Rong and M.Li Information Sciences 692 (2025) 121657 -->

<!-- 0.91 0.9 0 0.89 Average accuracy Φ 0.88 --Θ--average accuracy SRP divergence 0.87 ø average accuracy distance average accuracy Permutation JS divergence average accuracy our method 0.86 0.85 0.2 0.3 0.4 0.5 0.6 0.7 0.8 Proportion of training set -->
![](https://web-api.textin.com/ocr_image/external/c711d5c70ed12cdf.jpg)

Fig. 7. Comparisons of the methods on the Wine dataset.

**Table 16**

The specific performance of the four methods on the Iris dataset.


| Methods  | Descriptive statistics  | 20% | 30% | 40% | 50% | 60% | 70% | 80% |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| SRP divergence  | Average accuracy  | 0.8769  | 0.9006  | 0.9120  | 0.9227  | 0.9274  | 0.9314  | 0.9349  |
| SRP divergence  | Variance of accuracy  | 0.0050  | 0.0038  | 0.0025  | 0.0014  | 0.0016  | 0.0012  | 0.0016  |
| distance  | Average accuracy  | 0.9337  | 0.9386  | 0.9389  | 0.9381  | 0.9396  | 0.9397  | 0.9435  |
| distance  | Variance of accuracy  | 0.0004  | 0.0003  | 0.0003  | 0.0004  | 0.0006  | 0.0008  | 0.0015  |
| Permutation JS divergence  | Average accuracy  | 0.9203  | 0.9230  | 0.9217  | 0.9204  | 0.9223  | 0.9247  | 0.9300  |
| Permutation JS divergence  | Variance of accuracy  | 0.0004  | 0.0004  | 0.0004  | 0.0005  | 0.0008  | 0.0010  | 0.0020  |
| our method  | Average accuracy  | 0.9368  | 0.9410  | 0.9404  | 0.9407  | 0.9416  | 0.9424  | 0.9444  |
| our method  | Variance of accuracy  | 0.0003  | 0.0003  | 0.0003  | 0.0004  | 0.0006  | 0.0008  | 0.0016  |


**Table** 17

The specific performance of the four methods on the Wine dataset.


| Methods  | Descriptive statistics  | 20% | 30% | 40% | 50% | 60% | 70% | 80% |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| SRP divergence  | Average accuracy  | 0.9011  | 0.8912  | 0.8947  | 0.8847  | 0.8960  | 0.8938  | 0.8982  |
| SRP divergence  | Variance of accuracy  | 0.0007  | 0.0008  | 0.0006  | 0.0007  | 0.0009  | 0.0014  | 0.0020  |
| distance  | Average accuracy  | 0.8899  | 0.8826  | 0.8819  | 0.8745  | 0.8820  | 0.8842  | 0.8811  |
| distance  | Variance of accuracy  | 0.0008  | 0.0009  | 0.0007  | 0.0009  | 0.0009  | 0.0013  | 0.0020  |
| Permutation JS divergence  | Average accuracy  | 0.8812  | 0.8679  | 0.8641  | 0.8550  | 0.8554  | 0.8615  | 0.8547  |
| Permutation JS divergence  | Variance of accuracy  | 0.0008  | 0.0011  | 0.0009  | 0.0011  | 0.0011  | 0.0015  | 0.0021  |
| our method  | Average accuracy  | 0.9102  | 0.9000  | 0.9100  | 0.8963  | 0.9117  | 0.9042  | 0.9124  |
| our method  | Variance of accuracy  | 0.0009  | 0.0008  | 0.0006  | 0.0007  | 0.0008  | 0.0013  | 0.0017  |


on the quality of information after weighting rather than the original quality of the PMF before weighting, thereby enhancing the quality of the final results. Experiments have also demonstrated that our method performs well in practical engineering applications.

We did not present results for the direct application of LOS to fuse RPS due to the extensive number of potential outcomes.Nevertheless, we aim to demonstrate that the selection of fusion order significantly affects the final results. Our analysis,employing $RPS_{2}\oplus RPS_{3}\oplus RPS_{4},$  revealed that with training set ratios varying from 20% to 80%, the resultant fusion outcomes were suboptimal, maintaining a classification accuracy of approximately 73% in Iris dataset. This performance is notably inferior compared to any of the previously discussed methods. In contrast, our fusion method does not require knowledge of a specific fusion order and consistently achieves satisfactory results even in scenarios with a high number of attributes and a lack of prior knowledge regarding the appropriate fusion order.

<!-- 18 -->

<!-- L.Li,P.Rong and M.Li Information Sciences 692 (2025) 121657 -->

In summary, the experiments above demonstrate that our method has a clear advantage over other existing fusion methods in enhancing the quality of the final fusion results. Compared to directly using LOS or ROS for fusion, our approach significantly reduces computational costs when dealing with multiple sources of evidence, while still achieving a high quality of fusion.

## 7.Conclusion

The primary objective of this paper is to analyze and address the challenges encountered when directly applying the left orthogonal sum (LOS) and right orthogonal sum (ROS) to fuse Random Permutation Sets (RPSs) under conditions of extreme conflict across diverse scenarios. Through this investigation, we found that the traditional LOS and ROS approaches are limited by the inherent constraints of orthogonal fusion in evidence theory, particularly when processing highly conflicting information. To overcome these limitations,this paper introduces a novel fusion method that combines RPS distance and entropy to enhance the robustness and reliability of Permutation Mass Functions (PMFs). Our method begins by calculating the distance between RPSs to establish an optimal weighting of the initial RPSs, then selects the weighted subset RPS with maximum information quality through an entropy-based evaluation.This new approach has shown notable effectiveness in resolving conflicts, improving information quality, and delivering stable fusion outcomes, as demonstrated through several extreme-case scenarios discussed in this paper. Additionally, we applied our method in an air raid threat assessment experiment, where it demonstrated practical effectiveness in real-world scenarios, enabling higher-quality information for decision-making. By integrating our fusion method with a PMF generation algorithm, we also developed a classification algorithm applicable to real-world tasks. Our experiments compared this approach to three recently proposed fusion methods across multiple datasets, where our method consistently outperformed alternatives, both in data reliability and processing efficiency.Its practical value and effectiveness were further substantiated through real-world engineering applications, underscoring the robustness and adaptability of our proposed fusion technique.

As an extension of evidence theory, Random Permutation Sets (RPSs) offer significant potential for advancing both theoretical research and practical applications in various fields. RPSs provide a novel theoretical framework for addressing complex problems,including pattern recognition, risk assessment, medical diagnostics, and decision-making in uncertain environments. Their ability to represent uncertainty and handle conflicting evidence makes RPSs particularly valuable for scenarios where information is incomplete or contradictory. Furthermore, as real-world data is often subject to uncontrollable factors such as environmental noise and sensor inaccuracies, the need for effective fusion methods to resolve conflicts becomes critical. Our research on managing these conflict situations in RPS fusion has substantial practical implications, particularly in applications where reliable and accurate decision-making is crucial, such as in defense, healthcare, and financial risk analysis. By providing a systematic approach to mitigate these challenges, this paper contributes to the advancement of RPS theory and its applications, offering new opportunities for improving the quality and reliability of information fusion in various domains.

However, it is important to acknowledge that our method does have limitations. The primary limitation stems from the lengthy inference chain and the associated computational costs. Moving forward, our future efforts will focus on mitigating this limitation by exploring faster operational methods, akin to the matrix operations described in the reference [47], to streamline the issue of extended reasoning chains resulting from our approach.

# CRediT authorship contribution statement

**Linshan Li: Writing-original** draft. **Puhantong Rong: Methodology. Meizhu Li:**Writing-review & editing.

# Declaration of competing interest

The authors declare the following financial interests/personal relationships which may be considered as potential competing interests: Meizhu Li reports financial support was provided by National Natural Science Foundation of China. If there are other authors, they declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper.

# Acknowledgements

This work is supported by National Natural Science Foundation of China (Grant No. 62303198) and the Research Initiation Fund for Senior Talents of Jiangsu University (No. 23JDG010). We thank the technical support from Prof. Qi Zhang and the comments from anonymous reviewers for improving our manuscript.

# Data availability

No data was used for the research described in the article.

<!-- 19 -->

<!-- L.Li,P.Rong and M.Li Information Sciences 692(2025)121657 -->

# References

[1] B. Roy, Main sources of inaccurate determination, uncertainty and imprecision in decision models, in: Models and Methods in Multiple Criteria Decision Making,Elsevier, 1989, pp. 1245-1254.

[2] F. Xiao, A multiple-criteria decision-making method based on d numbers and belief entropy, Int. J. Fuzzy Syst. 21 (4) (2019) 1144-1153.

[3] S.Zhao,J. Liu, S. Wu, Multiple disease detection method for greenhouse-cultivated strawberry based on multiscale feature fusion faster r_cnn,Comput. Electron.Agric. 199 (2022)107176.

[4] X. Deng,W.Jiang,D number theory based game-theoretic framework in adversarial decision making under a fuzzy environment, Int. J. Approx. Reason. 106(2019)194-2213.

[5] Z.Liu, Q.Teng,Y. Song, W.Hao,Y.Liu,Y.Zhu, Y. Li, Hi-net: liver vessel segmentation with hierarchical inter-scale multi-scale feature fusion, Biomed.Signal Process. Control 96 (2024) 106604.

[6] A.P. Dempster, Upper and lower probabilities induced by a multivalued mapping, in: Classic Works of the Dempster-Shafer Theory of Belief Functions, Springer,2008,pp.57-72.

[7] G. Shafer, A Mathematical Theory of Evidence, vol. 42, Princeton University Press, 1976.

[8] R.R. Yager, Entailment for measure based belief structures, Inf. Fusion 47(2019)111-116.

[9] X. Su, L. Li, F. Shi, H. Qian, Research on the fusion of dependent evidence based on mutual information, IEEE Access 6 (2018) 71839-71845.

[10] G.Lin,J.Liang, Y. Qian, An information fusion approach by combining mmultigranulation rough sets and evidence theory, Inf. Sci. 314 (2015)184-199.[11]Y. Leung, N.-N. Ji, J.-H. Ma, An integrated information fusion approach based on the theory of evidence and group decision-making, Inf. Fusion 14 (4)(2013)410-422.

[12] C. Zhu, F.Xiao, A belief Hellinger distance for d-s evidence theory and its application in pattern recognition, Eng. Appl. Artif. Intell. 106(2021)104452.

[13] Z.-G. Liu, Y. Liu, J. Dezert, F. Cuzzolin, Evidence combination based on credal belief redistribution for pattern classification, IEEE Trans. Fuzzy Syst. 28 (4)(2019)618-631.

[14] B.Li, F.-W. Pang, An approach of vessel collision risk assessment based on the d-s evidence theory, Ocean Eng. 74(2013)16-21.

[15] Y.Pan,L. Zhang, Z. Li, L. DDing, Improved fuzzy Bayesian network-based risk analysis with interval-valued fuzzy sets and d-s evidence theory, IEEE Trans.Fuzzy Syst. 28 (9) (2019) 2063-2077.

[16] Y.Dong,J.Zhang, Z. Li,Y. Hu, Y. Deng, Combination of evidential sensor reports with distance function and belief entropy in fault diagnosis, Int. J. Comput.Commun.Control 14 (3)(2019)329-343.

[17] F.Xiao,W. Ding, Divergence measure of Pythagorean fuzzy sets and its application in medical diagnosis, Appl. Soft Comput. 79 (2019) 254-267.

[18] Y. Deng, Random permutation set, Int. J. Comput. Commun. Control 17 (1)(2022).

[19] J.Deng, Y. Deng, Maximum entropy of random permutation set, Soft Comput. 26 (21) (2022) 11265-11275.

[20] L.Chen, Y.Deng, K.H.Cheong, Permutation Jensen-Shannon divergence for random permutation set, Eng. Appl. Artif. Intell. 119 (2023) 105701.

[21] Q.Zhou, Y. Cui, Z. Li,Y.Deng, Marginalization in random permutation set theory: from the cooperative game perspective, Nonlinear Dyn. 111 (14)(2023)13125-13141.

[22] Q.Zhou, Y. Cui, W. Pedrycz, Y.Deng,Conjunctive and disjunctive combination rules in random permutation set theory: a layer-2 belief structure perspective,Inf. Fusion 102 (2024)102083.

[23] H. Xu, Y. Deng, Dependent evidence combination based on decision-making trial and evaluation laboratory method, Int. J. Intell. Syst. 34(7) (2019)1555-1571.

[24] L. Xiong, X. Su, H. Qian, Conflicting evidence combination from the perspective of networks, Inf. Sci. 580 (2021) 408-418.

[25] X.Chen, Y.Deng,A novel combination rule for conflict management in data fusion, Soft Comput. 27 (22) (2023) 16483-16492.

[26] Z.Yan, H.Zhao, X. Mei, An improved conflicting-evidence combination method based on the redistribution of the basic probability assignment,Appl. Intell.(2022)1-27.

[27] Y. Song, Y. Deng, A new method to measure the divergence in evidential sensor data fusion, Int. J. Distrib. Sens. Netw. 15 (4) (2019) 1550147719841295.

[28] F. Xiao, A new divergence measure for belief functions in d-s evidence theory for multisensor data fusion, Inf. Sci. 514 (2020) 462-483.

[29] E. Lefevre, O. Colot, P. Vannoorenberghe, Belief function combination and conflict management, Inf. Fusion 3(2)(2002)149-162.

[30] D.Yong,S.WenKang, Z. ZhenFu,L. Qi, Combining belief functions based on distance of evidence, Decis. Support Syst. 38 (3) (2004) 489-493.

[31] M. Li, L. Li, Q. Zhang, A new distance measure between two basic probability assignments based on penalty coefficient, Inf. Sci. (2024) 120883.

[32] A. Wehrl,General properties of entropy, Rev. Mod. Phys. 50 (2) (1978)221.

[33] P. Bromiley, N. Thacker, E. Bouhova-Thacker, Shannon entropy, Renyi entropy, and information, Stat. Inf. Ser. (2004-004) 9 (2004) (2004)2-8.

[34] H.Yan,Y.Deng, An improved belief entropy in evidence theory, IEEE Access 8 (2020) 57505-57516.

[35] Y. Deng, Deng entropy, Chaos Solitons Fractals 91 (2016) 549-553.

[36] T.Zhao, Z. Li, Y.Deng, Linearity in Deng entroppy, Chaos Solitons Fractals 178 (2024) 114388.

[37] Z. Yu, Y. Deng, Derive power law distribution with maximum Deng entropy, Chaos Solitons Fractals 165 (2022) 112877.

[38] H.Cui, Q.Liu, J.Zhang, B. Kang, An improved Deng entropy and its application in pattern recognition, IEEE Access 7 (2019) 18284-18292, https://doi.org/10.1109/ACCESS.2019.2896286.

[39] L. Chen, Y. Deng, Entropy of random permutation set, Commun. Stat., Theory Methods (2023)1-19.

[40] D.Li,Y.Deng,K.H.Cheong, Multisource basic probability assignment fusion based on information quality, Int. J. Intell. Syst. 36 (4) (2021) 1851-1875.

[41] L. Chen, Y. Deng, K.H. Cheong, The distance of random permutation set, Inf. Sci. 628 (2023) 226-239.

[42] A.-L. Jousselme, D. Grenier, É. Bossé, A new distance between two bodies of evidence, Inf. Fusion 2 (2) (2001) 91-101.

[43] L.Chen,Y.Deng,Entropy of random permutation set, Commun. Stat., Theory Methods 53 (11) (2024) 4127-4146.

[44] L.A. Zadeh, A simple view of the Dempster-Shafer theory of evidence and its implication for the rule of combination, AI Mag. 7 (2)(1986)85.

[45] J.Deng,Y. Deng, J.-B. Yang, Random permutation set reasoning, IEEE Trans. Pattern Anal. Mach.Intell.(2024).

[46] Z. Chen, R. Cai, Symmetric Renyi-permutation divergence and conflict management for random permutation set, Expert Syst. Appl. 238 (2024) 121784.

[47] W.Yang,Y.Deng,Matrix operations in random permutation set, Inf. Sci. 647 (2023)119419.

<!-- 20 -->

