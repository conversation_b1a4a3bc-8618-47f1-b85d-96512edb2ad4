%% TW-dRPS Distance System - 完全符合方案要求的实现
% 基于"总不一致度"模型的RPS距离计算系统
% 
% 核心改进:
% 1. 实现真正的"总不一致度"模型: OD_weighted = 1 - Total_Disagreement
% 2. 二元不一致度: Disagreement_d ∈ {0,1}
% 3. 位置权重分配: w_d 分配给位置d的重要性
% 4. 长度惩罚机制: L_long 处理不等长排列

clear; clc;

% 加载核心函数
run('core_functions.m');

fprintf('=== TW-dRPS Distance System - 符合方案要求的实现 ===\n');
fprintf('基于"总不一致度"模型的顶部加权RPS距离计算\n\n');

%% 1. 系统参数设置
fprintf('步骤1: 系统参数设置\n');
fprintf('==================\n');

% 设置系统参数
orness_original = 0.5;  % 原始方法等效的orness值
orness_improved = 0.8;  % 改进方法的orness值

fprintf('原始方法等效Orness: %.1f (均匀权重)\n', orness_original);
fprintf('改进方法Orness: %.2f (顶部偏置)\n', orness_improved);

%% 2. 创建测试RPS数据
fprintf('\n步骤2: 创建测试RPS数据\n');
fprintf('======================\n');

% 定义元素集合
elements = {'A', 'B', 'C', 'D'};
fprintf('元素集合: %s\n', strjoin(elements, ', '));

% 创建测试RPS - 设计为能最大化展示顶部偏置效果
% RPS1: A在顶部优先
rps1_pmf = containers.Map();
rps1_pmf('A,B,C') = 0.6;
rps1_pmf('A,C,B') = 0.3;
rps1_pmf('B,A,C') = 0.1;

% RPS2: B在顶部优先  
rps2_pmf = containers.Map();
rps2_pmf('B,A,C') = 0.7;
rps2_pmf('B,C,A') = 0.2;
rps2_pmf('A,B,C') = 0.1;

% RPS3: C在顶部优先
rps3_pmf = containers.Map();
rps3_pmf('C,A,B') = 0.5;
rps3_pmf('C,B,A') = 0.3;
rps3_pmf('A,C,B') = 0.2;

% 创建RPS结构
rps1 = create_rps(elements, rps1_pmf);
rps2 = create_rps(elements, rps2_pmf);
rps3 = create_rps(elements, rps3_pmf);

% 显示RPS信息
display_rps_detailed(rps1, 'RPS1 (A优先)');
display_rps_detailed(rps2, 'RPS2 (B优先)');
display_rps_detailed(rps3, 'RPS3 (C优先)');

%% 3. 权重生成验证
fprintf('\n步骤3: OWA权重生成验证\n');
fprintf('========================\n');

% 测试不同长度的权重生成
test_lengths = [2, 3, 4, 5];
fprintf('权重生成测试 (Orness = %.2f):\n', orness_improved);
fprintf('长度  权重分布\n');
fprintf('------------------\n');

for len = test_lengths
    weights = generate_owa_weights(len, orness_improved);
    fprintf('%d     [', len);
    for i = 1:len
        fprintf('%.4f', weights(i));
        if i < len, fprintf(', '); end
    end
    fprintf(']\n');
end

%% 4. 核心算法验证 - 详细展示改进的OD计算
fprintf('\n步骤4: 核心算法验证\n');
fprintf('==================\n');

% 选择两个具有代表性的排列进行详细分析
A = {'A', 'B', 'C'};  % 排列A
B = {'B', 'A', 'C'};  % 排列B (第1、2位交换)

fprintf('测试排列:\n');
fprintf('A = [%s]\n', strjoin(A, ', '));
fprintf('B = [%s]\n', strjoin(B, ', '));

% 原始方法计算
od_original = calculate_original_od(A, B);
fprintf('\n原始OD计算结果: %.6f\n', od_original);

% 改进方法计算 - 详细步骤展示
fprintf('\n改进OD计算详细步骤:\n');
od_improved = calculate_improved_od_detailed(A, B, orness_improved);
fprintf('改进OD计算结果: %.6f\n', od_improved);

fprintf('\n差异分析: 改进方法OD - 原始方法OD = %.6f\n', od_improved - od_original);

%% 5. 计算距离和相似度矩阵
fprintf('\n步骤5: 计算距离和相似度矩阵\n');
fprintf('============================\n');

rps_list = {rps1, rps2, rps3};
n_rps = length(rps_list);

% 计算距离矩阵
distance_matrix_orig = zeros(n_rps, n_rps);
distance_matrix_impr = zeros(n_rps, n_rps);

for i = 1:n_rps
    for j = 1:n_rps
        if i ~= j
            distance_matrix_orig(i,j) = calculate_rps_distance(rps_list{i}, rps_list{j}, 'original', orness_original);
            distance_matrix_impr(i,j) = calculate_rps_distance(rps_list{i}, rps_list{j}, 'improved', orness_improved);
        end
    end
end

% 计算相似度矩阵
sim_matrix_orig = 1 - distance_matrix_orig;
sim_matrix_impr = 1 - distance_matrix_impr;

for i = 1:n_rps
    sim_matrix_orig(i,i) = 1;
    sim_matrix_impr(i,i) = 1;
end

fprintf('距离矩阵对比:\n');
fprintf('原始方法距离矩阵:\n');
disp(distance_matrix_orig);
fprintf('改进方法距离矩阵:\n');
disp(distance_matrix_impr);

fprintf('相似度矩阵对比:\n');
fprintf('原始方法相似度矩阵:\n');
disp(sim_matrix_orig);
fprintf('改进方法相似度矩阵:\n');
disp(sim_matrix_impr);

%% 6. 计算支持度和可信度
fprintf('\n步骤6: 计算支持度和可信度\n');
fprintf('==========================\n');

% 计算支持度
support_orig = zeros(n_rps, 1);
support_impr = zeros(n_rps, 1);

for i = 1:n_rps
    support_orig(i) = sum(sim_matrix_orig(i, :)) - 1;
    support_impr(i) = sum(sim_matrix_impr(i, :)) - 1;
end

% 计算可信度
total_support_orig = sum(support_orig);
total_support_impr = sum(support_impr);
credibility_orig = support_orig / total_support_orig;
credibility_impr = support_impr / total_support_impr;

fprintf('支持度和可信度对比:\n');
fprintf('RPS    支持度(原始)  支持度(改进)  可信度(原始)  可信度(改进)\n');
fprintf('----------------------------------------------------------------\n');
for i = 1:n_rps
    fprintf('RPS%d   %.6f      %.6f      %.6f      %.6f\n', ...
        i, support_orig(i), support_impr(i), credibility_orig(i), credibility_impr(i));
end

%% 7. 计算加权RPS子集和熵值
fprintf('\n步骤7: 计算加权RPS子集和熵值\n');
fprintf('============================\n');

% 计算所有可能的两两组合
combinations_2 = nchoosek(1:n_rps, 2);
entropies_orig = [];
entropies_impr = [];

fprintf('所有两两组合的熵值:\n');
fprintf('组合      原始方法熵值  改进方法熵值  熵值差异\n');
fprintf('----------------------------------------------\n');

for c = 1:size(combinations_2, 1)
    idx1 = combinations_2(c, 1);
    idx2 = combinations_2(c, 2);
    
    % 原始方法权重
    weight_orig = [credibility_orig(idx1), credibility_orig(idx2)];
    weight_orig = weight_orig / sum(weight_orig);
    
    % 改进方法权重
    weight_impr = [credibility_impr(idx1), credibility_impr(idx2)];
    weight_impr = weight_impr / sum(weight_impr);
    
    % 计算加权PMF
    weighted_pmf_orig = weight_orig(1) * rps_list{idx1}.pmf_vector + weight_orig(2) * rps_list{idx2}.pmf_vector;
    weighted_pmf_impr = weight_impr(1) * rps_list{idx1}.pmf_vector + weight_impr(2) * rps_list{idx2}.pmf_vector;
    
    % 计算熵
    entropy_orig = calculate_rps_entropy(weighted_pmf_orig);
    entropy_impr = calculate_rps_entropy(weighted_pmf_impr);
    
    entropies_orig(c) = entropy_orig;
    entropies_impr(c) = entropy_impr;
    
    fprintf('RPS%d+RPS%d  %.6f        %.6f        %+.6f\n', ...
        idx1, idx2, entropy_orig, entropy_impr, entropy_impr - entropy_orig);
end

% 找到最小熵组合
[min_entropy_orig, min_idx_orig] = min(entropies_orig);
[min_entropy_impr, min_idx_impr] = min(entropies_impr);

fprintf('\n最优融合组合:\n');
fprintf('原始方法: RPS%d+RPS%d (熵值: %.6f)\n', ...
    combinations_2(min_idx_orig, 1), combinations_2(min_idx_orig, 2), min_entropy_orig);
fprintf('改进方法: RPS%d+RPS%d (熵值: %.6f)\n', ...
    combinations_2(min_idx_impr, 1), combinations_2(min_idx_impr, 2), min_entropy_impr);

%% 8. 融合结果对比表格
fprintf('\n步骤8: 融合结果对比表格\n');
fprintf('========================\n');

% 重新计算最优组合的加权PMF
idx1_orig = combinations_2(min_idx_orig, 1);
idx2_orig = combinations_2(min_idx_orig, 2);
weight_orig_optimal = [credibility_orig(idx1_orig), credibility_orig(idx2_orig)];
weight_orig_optimal = weight_orig_optimal / sum(weight_orig_optimal);
optimal_pmf_orig = weight_orig_optimal(1) * rps_list{idx1_orig}.pmf_vector + weight_orig_optimal(2) * rps_list{idx2_orig}.pmf_vector;

idx1_impr = combinations_2(min_idx_impr, 1);
idx2_impr = combinations_2(min_idx_impr, 2);
weight_impr_optimal = [credibility_impr(idx1_impr), credibility_impr(idx2_impr)];
weight_impr_optimal = weight_impr_optimal / sum(weight_impr_optimal);
optimal_pmf_impr = weight_impr_optimal(1) * rps_list{idx1_impr}.pmf_vector + weight_impr_optimal(2) * rps_list{idx2_impr}.pmf_vector;

% 创建融合结果对比表格
create_fusion_comparison_table(rps1, optimal_pmf_orig, optimal_pmf_impr, ...
    min_entropy_orig, min_entropy_impr, idx1_orig, idx2_orig, idx1_impr, idx2_impr);

%% 9. 结果分析和验证
fprintf('\n步骤9: 结果分析和验证\n');
fprintf('====================\n');

analyze_results(optimal_pmf_orig, optimal_pmf_impr, min_entropy_orig, min_entropy_impr, ...
    min_idx_orig, min_idx_impr, combinations_2);

fprintf('\n=== TW-dRPS系统验证完成 ===\n');