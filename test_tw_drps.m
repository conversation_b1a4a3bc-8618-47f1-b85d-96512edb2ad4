%% TW-dRPS测试脚本 - 验证改进算法的正确性

clear; clc;

% 加载核心函数
run('core_functions.m');

fprintf('=== TW-dRPS算法验证测试 ===\n\n');

%% 测试1: 验证"总不一致度"模型
fprintf('测试1: 验证"总不一致度"模型\n');
fprintf('============================\n');

% 测试用例：第1位交换 vs 第3位交换
A1 = {'A', 'B', 'C'};
B1 = {'B', 'A', 'C'};  % 第1、2位交换

A2 = {'A', 'B', 'C'};
B2 = {'A', 'C', 'B'};  % 第2、3位交换

orness_high = 0.9;  % 强顶部偏置
orness_low = 0.1;   % 弱顶部偏置

fprintf('测试排列:\n');
fprintf('A1 = [%s], B1 = [%s] (第1、2位交换)\n', strjoin(A1, ', '), strjoin(B1, ', '));
fprintf('A2 = [%s], B2 = [%s] (第2、3位交换)\n', strjoin(A2, ', '), strjoin(B2, ', '));

% 计算原始方法的OD
od_orig_1 = calculate_original_od(A1, B1);
od_orig_2 = calculate_original_od(A2, B2);

% 计算改进方法的OD（不同orness值）
od_impr_1_high = calculate_improved_od(A1, B1, orness_high);
od_impr_2_high = calculate_improved_od(A2, B2, orness_high);

od_impr_1_low = calculate_improved_od(A1, B1, orness_low);
od_impr_2_low = calculate_improved_od(A2, B2, orness_low);

fprintf('\n结果对比:\n');
fprintf('方法                第1、2位交换  第2、3位交换  差异\n');
fprintf('------------------------------------------------\n');
fprintf('原始方法            %.6f      %.6f      %.6f\n', od_orig_1, od_orig_2, od_orig_1 - od_orig_2);
fprintf('改进方法(α=%.1f)      %.6f      %.6f      %.6f\n', orness_high, od_impr_1_high, od_impr_2_high, od_impr_1_high - od_impr_2_high);
fprintf('改进方法(α=%.1f)      %.6f      %.6f      %.6f\n', orness_low, od_impr_1_low, od_impr_2_low, od_impr_1_low - od_impr_2_low);

fprintf('\n分析:\n');
if abs(od_orig_1 - od_orig_2) < 0.001
    fprintf('✓ 原始方法对两种交换给予相同惩罚（符合预期的缺陷）\n');
end

if (od_impr_1_high - od_impr_2_high) > 0.01
    fprintf('✓ 高orness值时，改进方法对顶部交换给予更大惩罚\n');
else
    fprintf('✗ 高orness值时，改进方法未能体现顶部偏置\n');
end

if abs(od_impr_1_low - od_impr_2_low) < abs(od_impr_1_high - od_impr_2_high)
    fprintf('✓ 低orness值时，改进方法的顶部偏置效果减弱\n');
end

%% 测试2: 验证长度惩罚机制
fprintf('\n\n测试2: 验证长度惩罚机制\n');
fprintf('========================\n');

A3 = {'A', 'B'};
B3 = {'A', 'B', 'C'};  % B3比A3长

A4 = {'A', 'B', 'C'};
B4 = {'A', 'B'};       % A4比B4长

fprintf('测试排列:\n');
fprintf('A3 = [%s], B3 = [%s] (长度: %d vs %d)\n', strjoin(A3, ', '), strjoin(B3, ', '), length(A3), length(B3));
fprintf('A4 = [%s], B4 = [%s] (长度: %d vs %d)\n', strjoin(A4, ', '), strjoin(B4, ', '), length(A4), length(B4));

od_impr_3 = calculate_improved_od(A3, B3, orness_high);
od_impr_4 = calculate_improved_od(A4, B4, orness_high);

fprintf('\n结果:\n');
fprintf('不等长排列的OD值:\n');
fprintf('A3 vs B3: %.6f\n', od_impr_3);
fprintf('A4 vs B4: %.6f\n', od_impr_4);

if od_impr_3 < 1 && od_impr_4 < 1
    fprintf('✓ 长度惩罚机制正常工作，不等长排列的OD值小于1\n');
else
    fprintf('✗ 长度惩罚机制可能存在问题\n');
end

%% 测试3: 验证权重生成的正确性
fprintf('\n\n测试3: 验证OWA权重生成\n');
fprintf('======================\n');

test_lengths = [3, 4, 5];
test_orness = [0.1, 0.5, 0.9];

fprintf('权重生成验证:\n');
fprintf('长度  Orness  权重分布                    计算Orness  误差\n');
fprintf('--------------------------------------------------------\n');

for len = test_lengths
    for orn = test_orness
        weights = generate_owa_weights(len, orn);
        calculated_orness = calculate_orness_from_weights(weights);
        error = abs(calculated_orness - orn);
        
        fprintf('%d     %.1f     [', len, orn);
        for i = 1:len
            fprintf('%.3f', weights(i));
            if i < len, fprintf(' '); end
        end
        fprintf(']     %.6f     %.2e\n', calculated_orness, error);
        
        if error > 1e-4
            fprintf('  ✗ 权重生成误差过大\n');
        end
    end
end

%% 测试4: 验证距离计算的单调性
fprintf('\n\n测试4: 验证距离计算的单调性\n');
fprintf('============================\n');

% 创建一系列具有不同冲突程度的RPS对
elements = {'A', 'B', 'C'};

% RPS1: 完全一致的情况
rps1_pmf = containers.Map();
rps1_pmf('A,B,C') = 1.0;

% RPS2: 轻微冲突（第2、3位交换）
rps2_pmf = containers.Map();
rps2_pmf('A,C,B') = 1.0;

% RPS3: 中等冲突（第1、2位交换）
rps3_pmf = containers.Map();
rps3_pmf('B,A,C') = 1.0;

% RPS4: 严重冲突（完全相反）
rps4_pmf = containers.Map();
rps4_pmf('C,B,A') = 1.0;

rps1 = create_rps(elements, rps1_pmf);
rps2 = create_rps(elements, rps2_pmf);
rps3 = create_rps(elements, rps3_pmf);
rps4 = create_rps(elements, rps4_pmf);

% 计算距离
dist_orig_12 = calculate_rps_distance(rps1, rps2, 'original', 0.5);
dist_orig_13 = calculate_rps_distance(rps1, rps3, 'original', 0.5);
dist_orig_14 = calculate_rps_distance(rps1, rps4, 'original', 0.5);

dist_impr_12 = calculate_rps_distance(rps1, rps2, 'improved', orness_high);
dist_impr_13 = calculate_rps_distance(rps1, rps3, 'improved', orness_high);
dist_impr_14 = calculate_rps_distance(rps1, rps4, 'improved', orness_high);

fprintf('距离单调性验证:\n');
fprintf('RPS对    冲突类型        原始方法    改进方法    改进效果\n');
fprintf('--------------------------------------------------------\n');
fprintf('RPS1-RPS2 轻微冲突(2,3位) %.6f    %.6f    %+.6f\n', dist_orig_12, dist_impr_12, dist_impr_12 - dist_orig_12);
fprintf('RPS1-RPS3 中等冲突(1,2位) %.6f    %.6f    %+.6f\n', dist_orig_13, dist_impr_13, dist_impr_13 - dist_orig_13);
fprintf('RPS1-RPS4 严重冲突(完全)  %.6f    %.6f    %+.6f\n', dist_orig_14, dist_impr_14, dist_impr_14 - dist_orig_14);

fprintf('\n单调性检查:\n');
if dist_orig_12 <= dist_orig_13 && dist_orig_13 <= dist_orig_14
    fprintf('✓ 原始方法保持距离单调性\n');
else
    fprintf('✗ 原始方法距离单调性异常\n');
end

if dist_impr_12 <= dist_impr_13 && dist_impr_13 <= dist_impr_14
    fprintf('✓ 改进方法保持距离单调性\n');
else
    fprintf('✗ 改进方法距离单调性异常\n');
end

% 检查顶部偏置效果
if (dist_impr_13 - dist_impr_12) > (dist_orig_13 - dist_orig_12)
    fprintf('✓ 改进方法增强了对顶部冲突的敏感性\n');
else
    fprintf('? 改进方法对顶部冲突的敏感性提升不明显\n');
end

%% 测试5: 边界情况测试
fprintf('\n\n测试5: 边界情况测试\n');
fprintf('==================\n');

% 测试空排列
empty1 = {};
empty2 = {};
single1 = {'A'};
single2 = {'B'};

od_empty = calculate_improved_od(empty1, empty2, 0.8);
od_single_same = calculate_improved_od(single1, single1, 0.8);
od_single_diff = calculate_improved_od(single1, single2, 0.8);

fprintf('边界情况测试结果:\n');
fprintf('空排列 vs 空排列: OD = %.6f (期望: 1.0)\n', od_empty);
fprintf('单元素相同: OD = %.6f (期望: 1.0)\n', od_single_same);
fprintf('单元素不同: OD = %.6f (期望: 0.0)\n', od_single_diff);

% 验证边界情况
boundary_tests_passed = 0;
total_boundary_tests = 3;

if abs(od_empty - 1.0) < 1e-6
    fprintf('✓ 空排列测试通过\n');
    boundary_tests_passed = boundary_tests_passed + 1;
else
    fprintf('✗ 空排列测试失败\n');
end

if abs(od_single_same - 1.0) < 1e-6
    fprintf('✓ 单元素相同测试通过\n');
    boundary_tests_passed = boundary_tests_passed + 1;
else
    fprintf('✗ 单元素相同测试失败\n');
end

if abs(od_single_diff - 0.0) < 1e-6
    fprintf('✓ 单元素不同测试通过\n');
    boundary_tests_passed = boundary_tests_passed + 1;
else
    fprintf('✗ 单元素不同测试失败\n');
end

%% 测试总结
fprintf('\n\n=== 测试总结 ===\n');
fprintf('边界情况测试: %d/%d 通过\n', boundary_tests_passed, total_boundary_tests);

if boundary_tests_passed == total_boundary_tests
    fprintf('✓ 所有边界情况测试通过\n');
else
    fprintf('✗ 部分边界情况测试失败，需要检查实现\n');
end

fprintf('\n核心改进验证:\n');
fprintf('1. 总不一致度模型: 已实现 OD_weighted = 1 - Total_Disagreement\n');
fprintf('2. 二元不一致度: 已实现 Disagreement_d ∈ {0,1}\n');
fprintf('3. 位置权重分配: 已实现基于OWA的位置重要性权重\n');
fprintf('4. 长度惩罚机制: 已实现L_long和长度差异处理\n');
fprintf('5. 顶部偏置效果: 通过orness参数控制顶部敏感性\n');

fprintf('\n=== TW-dRPS算法验证完成 ===\n');